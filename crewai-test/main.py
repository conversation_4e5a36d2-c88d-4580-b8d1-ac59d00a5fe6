from crewai import Agent, Task, Crew
from langchain.chat_models import ChatOpenAI
from dotenv import load_dotenv
import os

load_dotenv()

# Define Agent
test_agent = Agent(
    role="Tester",
    goal="Test basic communication and return a message",
    backstory="A minimal agent to test CrewAI setup.",
    verbose=True,
    llm=ChatOpenAI(model="gpt-3.5-turbo", temperature=0.2)
)

# Define Task
task = Task(
    description="Say hello and confirm setup is working",
    agent=test_agent
)

# Create Crew and run
crew = Crew(
    agents=[test_agent],
    tasks=[task],
    verbose=2
)

crew.kickoff()
