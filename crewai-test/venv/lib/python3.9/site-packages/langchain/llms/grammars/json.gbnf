# Grammar for subset of JSON - doesn't support full string or number syntax

root  ::= object
value ::= object | array | string | number | boolean | "null"

object ::=
  "{" ws (
            string ":" ws value
    ("," ws string ":" ws value)*
  )? "}"

array  ::=
  "[" ws (
            value
    ("," ws value)*
  )? "]"

string  ::=
  "\"" (
    [^"\\] |
    "\\" (["\\/bfnrt] | "u" [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F] [0-9a-fA-F]) # escapes
  )* "\"" ws

# Only plain integers currently
number  ::= "-"? [0-9]+ ws
boolean ::= ("true" | "false") ws

# Optional space: by convention, applied in this grammar after literal chars when allowed
ws ::= ([ \t\n] ws)?