Metadata-Version: 2.1
Name: langchain-community
Version: 0.0.20
Summary: Community contributed LangChain integrations.
Home-page: https://github.com/langchain-ai/langchain
License: MIT
Requires-Python: >=3.8.1,<4.0
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Provides-Extra: cli
Provides-Extra: extended-testing
Requires-Dist: PyYAML (>=5.3)
Requires-Dist: SQLAlchemy (>=1.4,<3)
Requires-Dist: aiohttp (>=3.8.3,<4.0.0)
Requires-Dist: aiosqlite (>=0.19.0,<0.20.0) ; extra == "extended-testing"
Requires-Dist: aleph-alpha-client (>=2.15.0,<3.0.0) ; extra == "extended-testing"
Requires-Dist: anthropic (>=0.3.11,<0.4.0) ; extra == "extended-testing"
Requires-Dist: arxiv (>=1.4,<2.0) ; extra == "extended-testing"
Requires-Dist: assemblyai (>=0.17.0,<0.18.0) ; extra == "extended-testing"
Requires-Dist: atlassian-python-api (>=3.36.0,<4.0.0) ; extra == "extended-testing"
Requires-Dist: azure-ai-documentintelligence (>=1.0.0b1,<2.0.0) ; extra == "extended-testing"
Requires-Dist: beautifulsoup4 (>=4,<5) ; extra == "extended-testing"
Requires-Dist: bibtexparser (>=1.4.0,<2.0.0) ; extra == "extended-testing"
Requires-Dist: cassio (>=0.1.0,<0.2.0) ; extra == "extended-testing"
Requires-Dist: chardet (>=5.1.0,<6.0.0) ; extra == "extended-testing"
Requires-Dist: cohere (>=4,<5) ; extra == "extended-testing"
Requires-Dist: databricks-vectorsearch (>=0.21,<0.22) ; extra == "extended-testing"
Requires-Dist: dataclasses-json (>=0.5.7,<0.7)
Requires-Dist: datasets (>=2.15.0,<3.0.0) ; extra == "extended-testing"
Requires-Dist: dgml-utils (>=0.3.0,<0.4.0) ; extra == "extended-testing"
Requires-Dist: elasticsearch (>=8.12.0,<9.0.0) ; extra == "extended-testing"
Requires-Dist: esprima (>=4.0.1,<5.0.0) ; extra == "extended-testing"
Requires-Dist: faiss-cpu (>=1,<2) ; extra == "extended-testing"
Requires-Dist: feedparser (>=6.0.10,<7.0.0) ; extra == "extended-testing"
Requires-Dist: fireworks-ai (>=0.9.0,<0.10.0) ; extra == "extended-testing"
Requires-Dist: geopandas (>=0.13.1,<0.14.0) ; extra == "extended-testing"
Requires-Dist: gitpython (>=3.1.32,<4.0.0) ; extra == "extended-testing"
Requires-Dist: google-cloud-documentai (>=2.20.1,<3.0.0) ; extra == "extended-testing"
Requires-Dist: gql (>=3.4.1,<4.0.0) ; extra == "extended-testing"
Requires-Dist: gradientai (>=1.4.0,<2.0.0) ; extra == "extended-testing"
Requires-Dist: hdbcli (>=2.19.21,<3.0.0) ; extra == "extended-testing"
Requires-Dist: hologres-vector (>=0.0.6,<0.0.7) ; extra == "extended-testing"
Requires-Dist: html2text (>=2020.1.16,<2021.0.0) ; extra == "extended-testing"
Requires-Dist: httpx (>=0.24.1,<0.25.0) ; extra == "extended-testing"
Requires-Dist: javelin-sdk (>=0.1.8,<0.2.0) ; extra == "extended-testing"
Requires-Dist: jinja2 (>=3,<4) ; extra == "extended-testing"
Requires-Dist: jq (>=1.4.1,<2.0.0) ; extra == "extended-testing"
Requires-Dist: jsonschema (>1) ; extra == "extended-testing"
Requires-Dist: langchain-core (>=0.1.21,<0.2)
Requires-Dist: langsmith (>=0.0.83,<0.1)
Requires-Dist: lxml (>=4.9.2,<5.0.0) ; extra == "extended-testing"
Requires-Dist: markdownify (>=0.11.6,<0.12.0) ; extra == "extended-testing"
Requires-Dist: motor (>=3.3.1,<4.0.0) ; extra == "extended-testing"
Requires-Dist: msal (>=1.25.0,<2.0.0) ; extra == "extended-testing"
Requires-Dist: mwparserfromhell (>=0.6.4,<0.7.0) ; extra == "extended-testing"
Requires-Dist: mwxml (>=0.3.3,<0.4.0) ; extra == "extended-testing"
Requires-Dist: newspaper3k (>=0.2.8,<0.3.0) ; extra == "extended-testing"
Requires-Dist: numexpr (>=2.8.6,<3.0.0) ; extra == "extended-testing"
Requires-Dist: numpy (>=1,<2)
Requires-Dist: nvidia-riva-client (>=2.14.0,<3.0.0) ; extra == "extended-testing"
Requires-Dist: oci (>=2.119.1,<3.0.0) ; extra == "extended-testing"
Requires-Dist: openai (<2) ; extra == "extended-testing"
Requires-Dist: openapi-pydantic (>=0.3.2,<0.4.0) ; extra == "extended-testing"
Requires-Dist: oracle-ads (>=2.9.1,<3.0.0) ; extra == "extended-testing"
Requires-Dist: pandas (>=2.0.1,<3.0.0) ; extra == "extended-testing"
Requires-Dist: pdfminer-six (>=20221105,<20221106) ; extra == "extended-testing"
Requires-Dist: pgvector (>=0.1.6,<0.2.0) ; extra == "extended-testing"
Requires-Dist: praw (>=7.7.1,<8.0.0) ; extra == "extended-testing"
Requires-Dist: psychicapi (>=0.8.0,<0.9.0) ; extra == "extended-testing"
Requires-Dist: py-trello (>=0.19.0,<0.20.0) ; extra == "extended-testing"
Requires-Dist: pymupdf (>=1.22.3,<2.0.0) ; extra == "extended-testing"
Requires-Dist: pypdf (>=3.4.0,<4.0.0) ; extra == "extended-testing"
Requires-Dist: pypdfium2 (>=4.10.0,<5.0.0) ; extra == "extended-testing"
Requires-Dist: pyspark (>=3.4.0,<4.0.0) ; extra == "extended-testing"
Requires-Dist: rank-bm25 (>=0.2.2,<0.3.0) ; extra == "extended-testing"
Requires-Dist: rapidfuzz (>=3.1.1,<4.0.0) ; extra == "extended-testing"
Requires-Dist: rapidocr-onnxruntime (>=1.3.2,<2.0.0) ; (python_full_version >= "3.8.1" and python_version < "3.12") and (extra == "extended-testing")
Requires-Dist: rdflib (==7.0.0) ; extra == "extended-testing"
Requires-Dist: requests (>=2,<3)
Requires-Dist: requests-toolbelt (>=1.0.0,<2.0.0) ; extra == "extended-testing"
Requires-Dist: rspace_client (>=2.5.0,<3.0.0) ; extra == "extended-testing"
Requires-Dist: scikit-learn (>=1.2.2,<2.0.0) ; extra == "extended-testing"
Requires-Dist: sqlite-vss (>=0.1.2,<0.2.0) ; extra == "extended-testing"
Requires-Dist: streamlit (>=1.18.0,<2.0.0) ; (python_full_version >= "3.8.1" and python_full_version != "3.9.7" and python_version < "4.0") and (extra == "extended-testing")
Requires-Dist: sympy (>=1.12,<2.0) ; extra == "extended-testing"
Requires-Dist: telethon (>=1.28.5,<2.0.0) ; extra == "extended-testing"
Requires-Dist: tenacity (>=8.1.0,<9.0.0)
Requires-Dist: timescale-vector (>=0.0.1,<0.0.2) ; extra == "extended-testing"
Requires-Dist: tqdm (>=4.48.0) ; extra == "extended-testing"
Requires-Dist: tree-sitter (>=0.20.2,<0.21.0) ; extra == "extended-testing"
Requires-Dist: tree-sitter-languages (>=1.8.0,<2.0.0) ; extra == "extended-testing"
Requires-Dist: typer (>=0.9.0,<0.10.0) ; extra == "cli"
Requires-Dist: upstash-redis (>=0.15.0,<0.16.0) ; extra == "extended-testing"
Requires-Dist: xata (>=1.0.0a7,<2.0.0) ; extra == "extended-testing"
Requires-Dist: xmltodict (>=0.13.0,<0.14.0) ; extra == "extended-testing"
Requires-Dist: zhipuai (>=1.0.7,<2.0.0) ; extra == "extended-testing"
Project-URL: Repository, https://github.com/langchain-ai/langchain
Description-Content-Type: text/markdown

# 🦜️🧑‍🤝‍🧑 LangChain Community

[![Downloads](https://static.pepy.tech/badge/langchain_community/month)](https://pepy.tech/project/langchain_community)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Quick Install

```bash
pip install langchain-community
```

## What is it?

LangChain Community contains third-party integrations that implement the base interfaces defined in LangChain Core, making them ready-to-use in any LangChain application.

For full documentation see the [API reference](https://api.python.langchain.com/en/stable/community_api_reference.html).

![Diagram outlining the hierarchical organization of the LangChain framework, displaying the interconnected parts across multiple layers.](../../docs/static/img/langchain_stack.png "LangChain Framework Overview")

## 📕 Releases & Versioning

`langchain-community` is currently on version `0.0.x`

All changes will be accompanied by a patch version increase.

## 💁 Contributing

As an open-source project in a rapidly developing field, we are extremely open to contributions, whether it be in the form of a new feature, improved infrastructure, or better documentation.

For detailed information on how to contribute, see the [Contributing Guide](https://python.langchain.com/docs/contributing/).
