# Apify Service for LinkedIn Data Scraping

import asyncio
import aiohttp
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class ApifyConfig(BaseModel):
    api_token: str
    actor_id: str = "apify/linkedin-profile-scraper"  # Popular LinkedIn scraper
    timeout: int = 300  # 5 minutes
    max_retries: int = 3

class LinkedInSearchParams(BaseModel):
    keywords: List[str]
    locations: Optional[List[str]] = []
    industries: Optional[List[str]] = []
    company_sizes: Optional[List[str]] = []
    seniority_levels: Optional[List[str]] = []
    max_results: int = 1000
    include_private_profiles: bool = False

class ApifyService:
    def __init__(self, config: ApifyConfig):
        self.config = config
        self.base_url = "https://api.apify.com/v2"
        
    async def start_linkedin_scraping(self, search_params: LinkedInSearchParams) -> str:
        """
        Start a LinkedIn scraping job using Apify actor
        Returns the run ID for tracking
        """
        actor_input = self._build_actor_input(search_params)
        
        async with aiohttp.ClientSession() as session:
            url = f"{self.base_url}/acts/{self.config.actor_id}/runs"
            headers = {
                "Authorization": f"Bearer {self.config.api_token}",
                "Content-Type": "application/json"
            }
            
            try:
                async with session.post(url, json=actor_input, headers=headers) as response:
                    if response.status == 201:
                        result = await response.json()
                        run_id = result["data"]["id"]
                        logger.info(f"Started Apify run: {run_id}")
                        return run_id
                    else:
                        error_text = await response.text()
                        raise Exception(f"Failed to start Apify run: {error_text}")
                        
            except Exception as e:
                logger.error(f"Error starting Apify run: {e}")
                raise

    async def get_run_status(self, run_id: str) -> Dict[str, Any]:
        """Get the status of a running Apify job"""
        async with aiohttp.ClientSession() as session:
            url = f"{self.base_url}/actor-runs/{run_id}"
            headers = {"Authorization": f"Bearer {self.config.api_token}"}
            
            try:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["data"]
                    else:
                        error_text = await response.text()
                        raise Exception(f"Failed to get run status: {error_text}")
                        
            except Exception as e:
                logger.error(f"Error getting run status: {e}")
                raise

    async def get_run_results(self, run_id: str) -> List[Dict[str, Any]]:
        """Get the results from a completed Apify run"""
        async with aiohttp.ClientSession() as session:
            url = f"{self.base_url}/actor-runs/{run_id}/dataset/items"
            headers = {"Authorization": f"Bearer {self.config.api_token}"}
            
            try:
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        results = await response.json()
                        logger.info(f"Retrieved {len(results)} results from run {run_id}")
                        return results
                    else:
                        error_text = await response.text()
                        raise Exception(f"Failed to get run results: {error_text}")
                        
            except Exception as e:
                logger.error(f"Error getting run results: {e}")
                raise

    async def wait_for_completion(self, run_id: str, poll_interval: int = 30) -> List[Dict[str, Any]]:
        """
        Wait for an Apify run to complete and return results
        """
        max_wait_time = self.config.timeout
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            status_data = await self.get_run_status(run_id)
            status = status_data.get("status")
            
            logger.info(f"Run {run_id} status: {status}")
            
            if status == "SUCCEEDED":
                return await self.get_run_results(run_id)
            elif status in ["FAILED", "ABORTED", "TIMED-OUT"]:
                raise Exception(f"Apify run failed with status: {status}")
            
            await asyncio.sleep(poll_interval)
            elapsed_time += poll_interval
            
        raise Exception(f"Apify run timed out after {max_wait_time} seconds")

    def _build_actor_input(self, search_params: LinkedInSearchParams) -> Dict[str, Any]:
        """Build the input configuration for the Apify actor"""
        
        # Build search query from keywords
        search_query = " OR ".join(search_params.keywords)
        
        # Build location filter
        location_filter = ""
        if search_params.locations:
            location_filter = " OR ".join(search_params.locations)
        
        actor_input = {
            "searchQuery": search_query,
            "maxResults": search_params.max_results,
            "includePrivateProfiles": search_params.include_private_profiles,
            "saveToDataset": True,
            "proxyConfiguration": {
                "useApifyProxy": True,
                "apifyProxyGroups": ["RESIDENTIAL"]
            }
        }
        
        # Add location filter if specified
        if location_filter:
            actor_input["locationFilter"] = location_filter
            
        # Add industry filter if specified
        if search_params.industries:
            actor_input["industryFilter"] = search_params.industries
            
        # Add company size filter if specified
        if search_params.company_sizes:
            actor_input["companySizeFilter"] = search_params.company_sizes
            
        # Add seniority level filter if specified
        if search_params.seniority_levels:
            actor_input["seniorityFilter"] = search_params.seniority_levels
        
        return actor_input

    def transform_apify_data(self, raw_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Transform raw Apify data into our standardized lead format
        """
        transformed_leads = []
        
        for item in raw_data:
            try:
                lead = {
                    "linkedin_url": item.get("url", ""),
                    "full_name": item.get("fullName", ""),
                    "headline": item.get("headline", ""),
                    "location": item.get("location", ""),
                    "company": item.get("company", ""),
                    "company_size": item.get("companySize", ""),
                    "industry": item.get("industry", ""),
                    "experience_years": self._extract_experience_years(item.get("experience", [])),
                    "education": item.get("education", []),
                    "skills": item.get("skills", []),
                    "contact_info": {
                        "email": item.get("email", ""),
                        "phone": item.get("phone", ""),
                        "website": item.get("website", "")
                    },
                    "profile_image_url": item.get("profileImageUrl", ""),
                    "connections_count": item.get("connectionsCount", 0),
                    "scraped_at": datetime.utcnow()
                }
                
                transformed_leads.append(lead)
                
            except Exception as e:
                logger.warning(f"Error transforming lead data: {e}")
                continue
                
        return transformed_leads

    def _extract_experience_years(self, experience_data: List[Dict]) -> Optional[int]:
        """Extract total years of experience from experience data"""
        if not experience_data:
            return None
            
        total_months = 0
        for exp in experience_data:
            start_date = exp.get("startDate")
            end_date = exp.get("endDate", "Present")
            
            # Simple calculation - you might want to make this more sophisticated
            if start_date:
                # Assume each position adds 2 years on average if dates are unclear
                total_months += 24
                
        return total_months // 12 if total_months > 0 else None

# Example usage
async def example_usage():
    config = ApifyConfig(
        api_token="your_apify_token_here",
        actor_id="apify/linkedin-profile-scraper"
    )
    
    service = ApifyService(config)
    
    search_params = LinkedInSearchParams(
        keywords=["software engineer", "python developer"],
        locations=["San Francisco", "New York"],
        industries=["Technology", "Software"],
        max_results=500
    )
    
    # Start scraping
    run_id = await service.start_linkedin_scraping(search_params)
    
    # Wait for completion and get results
    results = await service.wait_for_completion(run_id)
    
    # Transform data
    leads = service.transform_apify_data(results)
    
    print(f"Scraped {len(leads)} leads")
    return leads
