# LinkedIn Lead Generation App - Architecture Design

## 🏗️ High-Level Architecture Overview

### Tech Stack
- **Frontend**: React 18 + TypeScript + Tailwind CSS + Vite
- **Backend**: FastAPI + Python 3.10+ + Pydantic
- **Database**: PostgreSQL (primary) + Redis (cache/sessions)
- **AI/ML**: CrewAI + OpenAI GPT-4 + <PERSON><PERSON>hain
- **Data Scraping**: Apify LinkedIn Actors
- **Authentication**: JWT + OAuth2
- **Deployment**: Docker + AWS/GCP
- **Monitoring**: Sentry + DataDog

## 📊 Database Schema

### Core Tables

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    company VARCHAR(255),
    subscription_tier VARCHAR(50) DEFAULT 'free',
    credits_remaining INTEGER DEFAULT 100,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Search campaigns
CREATE TABLE campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    keywords TEXT[] NOT NULL,
    target_locations TEXT[],
    target_industries TEXT[],
    target_company_sizes TEXT[],
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Lead profiles
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    linkedin_url VARCHAR(500) UNIQUE,
    full_name VARCHAR(255),
    headline VARCHAR(500),
    location VARCHAR(255),
    company VARCHAR(255),
    company_size VARCHAR(100),
    industry VARCHAR(255),
    experience_years INTEGER,
    education JSONB,
    skills TEXT[],
    ai_score DECIMAL(3,2), -- 0.00 to 1.00
    ai_reasoning TEXT,
    contact_info JSONB,
    scraped_at TIMESTAMP DEFAULT NOW(),
    processed_at TIMESTAMP,
    status VARCHAR(50) DEFAULT 'new'
);

-- AI scoring history
CREATE TABLE scoring_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lead_id UUID REFERENCES leads(id) ON DELETE CASCADE,
    score DECIMAL(3,2),
    reasoning TEXT,
    model_version VARCHAR(50),
    scored_at TIMESTAMP DEFAULT NOW()
);

-- Export jobs
CREATE TABLE exports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES campaigns(id),
    format VARCHAR(20), -- 'csv', 'xlsx', 'json'
    filters JSONB,
    file_path VARCHAR(500),
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);
```

## 🔌 API Design

### Authentication Endpoints
```
POST /auth/register
POST /auth/login
POST /auth/refresh
POST /auth/logout
GET  /auth/me
```

### Campaign Management
```
GET    /campaigns                    # List user campaigns
POST   /campaigns                    # Create new campaign
GET    /campaigns/{id}               # Get campaign details
PUT    /campaigns/{id}               # Update campaign
DELETE /campaigns/{id}               # Delete campaign
POST   /campaigns/{id}/start         # Start lead generation
POST   /campaigns/{id}/stop          # Stop lead generation
```

### Lead Management
```
GET    /leads                        # List leads with filters
GET    /leads/{id}                   # Get lead details
PUT    /leads/{id}                   # Update lead status/notes
DELETE /leads/{id}                   # Delete lead
POST   /leads/bulk-action            # Bulk operations
GET    /leads/analytics              # Lead analytics
```

### AI Scoring
```
POST   /ai/score-lead/{id}           # Score individual lead
POST   /ai/score-batch               # Batch scoring
GET    /ai/scoring-criteria          # Get scoring criteria
PUT    /ai/scoring-criteria          # Update scoring criteria
```

### Export & Integration
```
POST   /exports                      # Create export job
GET    /exports/{id}                 # Get export status
GET    /exports/{id}/download        # Download export file
POST   /integrations/crm             # CRM integration
```

## 🎯 Detailed Component Design

### 1. Frontend Architecture (React + TypeScript)

#### Component Structure
```
src/
├── components/
│   ├── common/
│   │   ├── Button.tsx
│   │   ├── Input.tsx
│   │   ├── Modal.tsx
│   │   └── Table.tsx
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── Layout.tsx
│   ├── campaigns/
│   │   ├── CampaignForm.tsx
│   │   ├── CampaignList.tsx
│   │   └── CampaignDetails.tsx
│   └── leads/
│       ├── LeadCard.tsx
│       ├── LeadTable.tsx
│       ├── LeadFilters.tsx
│       └── LeadDetails.tsx
├── pages/
│   ├── Dashboard.tsx
│   ├── Campaigns.tsx
│   ├── Leads.tsx
│   └── Analytics.tsx
├── hooks/
│   ├── useAuth.ts
│   ├── useCampaigns.ts
│   └── useLeads.ts
├── services/
│   ├── api.ts
│   ├── auth.ts
│   └── types.ts
└── utils/
    ├── constants.ts
    └── helpers.ts
```

#### Key Features
- **Responsive Design**: Mobile-first with Tailwind CSS
- **Real-time Updates**: WebSocket for live campaign status
- **Data Visualization**: Charts for lead analytics
- **Infinite Scroll**: For large lead datasets
- **Export Functionality**: CSV/Excel download
- **Search & Filters**: Advanced filtering options

### 2. Backend Architecture (FastAPI)

#### Service Layer Structure
```
app/
├── api/
│   ├── v1/
│   │   ├── auth.py
│   │   ├── campaigns.py
│   │   ├── leads.py
│   │   └── exports.py
├── core/
│   ├── config.py
│   ├── security.py
│   └── database.py
├── services/
│   ├── apify_service.py
│   ├── ai_scoring_service.py
│   ├── lead_processing_service.py
│   └── export_service.py
├── models/
│   ├── user.py
│   ├── campaign.py
│   └── lead.py
├── schemas/
│   ├── auth.py
│   ├── campaign.py
│   └── lead.py
└── workers/
    ├── scraping_worker.py
    └── scoring_worker.py
```

### 3. AI Scoring Engine (CrewAI Integration)

#### Scoring Criteria
- **Profile Completeness** (20%): LinkedIn profile completion
- **Relevance Score** (30%): Keyword match in headline/summary
- **Seniority Level** (25%): Job title analysis
- **Company Fit** (15%): Company size/industry match
- **Engagement Potential** (10%): Activity indicators

#### CrewAI Agent Configuration
```python
lead_scorer_agent = Agent(
    role="Lead Qualification Specialist",
    goal="Analyze LinkedIn profiles and score lead quality",
    backstory="Expert in B2B lead qualification with deep understanding of professional profiles",
    tools=[profile_analyzer, company_matcher, keyword_scorer],
    llm=ChatOpenAI(model="gpt-4", temperature=0.1)
)
```
