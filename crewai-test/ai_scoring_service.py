# AI Scoring Service using CrewAI for Lead Qualification

from crewai import Agent, Task, Crew
from langchain_openai import ChatOpenAI
from typing import Dict, List, Any, Optional
import json
import logging
from datetime import datetime
from pydantic import BaseModel

logger = logging.getLogger(__name__)

class ScoringCriteria(BaseModel):
    profile_completeness_weight: float = 0.20
    relevance_weight: float = 0.30
    seniority_weight: float = 0.25
    company_fit_weight: float = 0.15
    engagement_potential_weight: float = 0.10

class LeadProfile(BaseModel):
    full_name: str
    headline: str
    location: str
    company: str
    company_size: str
    industry: str
    experience_years: Optional[int]
    education: List[Dict[str, Any]]
    skills: List[str]
    connections_count: int
    linkedin_url: str

class ScoringResult(BaseModel):
    overall_score: float
    criteria_breakdown: Dict[str, float]
    reasoning: str
    recommendations: List[str]

class AILeadScoringService:
    def __init__(self, openai_api_key: str, model: str = "gpt-4"):
        self.llm = ChatOpenAI(
            model=model,
            temperature=0.1,
            openai_api_key=openai_api_key
        )
        self.criteria = ScoringCriteria()
        self._setup_agents()

    def _setup_agents(self):
        """Setup CrewAI agents for lead scoring"""
        
        # Profile Analyzer Agent
        self.profile_analyzer = Agent(
            role="LinkedIn Profile Analyzer",
            goal="Analyze LinkedIn profiles for completeness and professional quality",
            backstory="""You are an expert at evaluating LinkedIn profiles. You understand what makes 
            a profile complete, professional, and indicative of an engaged professional. You look at 
            profile completeness, quality of headline, education background, and overall presentation.""",
            verbose=True,
            llm=self.llm
        )
        
        # Relevance Scorer Agent
        self.relevance_scorer = Agent(
            role="Keyword Relevance Specialist",
            goal="Score how well a profile matches target keywords and criteria",
            backstory="""You are a specialist in matching professional profiles to specific business 
            requirements. You excel at identifying relevant skills, experience, and industry alignment 
            based on keywords and target criteria.""",
            verbose=True,
            llm=self.llm
        )
        
        # Seniority Assessor Agent
        self.seniority_assessor = Agent(
            role="Professional Seniority Evaluator",
            goal="Assess the seniority level and decision-making authority of professionals",
            backstory="""You are an expert in organizational hierarchies and professional levels. 
            You can determine seniority based on job titles, years of experience, company size, 
            and responsibilities indicated in profiles.""",
            verbose=True,
            llm=self.llm
        )
        
        # Lead Qualification Manager
        self.lead_qualifier = Agent(
            role="Lead Qualification Manager",
            goal="Synthesize all scoring criteria into a final lead quality score",
            backstory="""You are a senior sales operations manager with extensive experience in 
            lead qualification. You understand how to weight different factors to predict lead 
            quality and conversion potential.""",
            verbose=True,
            llm=self.llm
        )

    async def score_lead(
        self, 
        profile: LeadProfile, 
        target_keywords: List[str],
        target_criteria: Dict[str, Any],
        custom_weights: Optional[ScoringCriteria] = None
    ) -> ScoringResult:
        """Score a single lead using AI analysis"""
        
        if custom_weights:
            self.criteria = custom_weights
            
        try:
            # Create tasks for each scoring dimension
            tasks = self._create_scoring_tasks(profile, target_keywords, target_criteria)
            
            # Create crew and execute scoring
            crew = Crew(
                agents=[
                    self.profile_analyzer,
                    self.relevance_scorer,
                    self.seniority_assessor,
                    self.lead_qualifier
                ],
                tasks=tasks,
                verbose=True
            )
            
            # Execute the crew
            result = crew.kickoff()
            
            # Parse and structure the result
            scoring_result = self._parse_scoring_result(result)
            
            logger.info(f"Scored lead {profile.full_name}: {scoring_result.overall_score}")
            return scoring_result
            
        except Exception as e:
            logger.error(f"Error scoring lead {profile.full_name}: {e}")
            # Return default low score on error
            return ScoringResult(
                overall_score=0.1,
                criteria_breakdown={
                    "profile_completeness": 0.1,
                    "relevance": 0.1,
                    "seniority": 0.1,
                    "company_fit": 0.1,
                    "engagement_potential": 0.1
                },
                reasoning="Error occurred during scoring analysis",
                recommendations=["Manual review required due to scoring error"]
            )

    def _create_scoring_tasks(
        self, 
        profile: LeadProfile, 
        target_keywords: List[str],
        target_criteria: Dict[str, Any]
    ) -> List[Task]:
        """Create scoring tasks for each agent"""
        
        profile_data = self._format_profile_for_analysis(profile)
        
        # Profile Completeness Task
        profile_task = Task(
            description=f"""
            Analyze this LinkedIn profile for completeness and professional quality:
            
            {profile_data}
            
            Score the profile completeness from 0.0 to 1.0 based on:
            - Profile photo presence
            - Headline quality and descriptiveness
            - Summary/about section completeness
            - Work experience detail level
            - Education information
            - Skills listed
            - Connection count (indicator of engagement)
            
            Provide a score and brief reasoning.
            """,
            expected_output="A completeness score (0.0-1.0) with reasoning",
            agent=self.profile_analyzer
        )
        
        # Relevance Scoring Task
        relevance_task = Task(
            description=f"""
            Score how well this profile matches our target criteria:
            
            Profile: {profile_data}
            Target Keywords: {', '.join(target_keywords)}
            Target Criteria: {json.dumps(target_criteria, indent=2)}
            
            Score relevance from 0.0 to 1.0 based on:
            - Keyword matches in headline and experience
            - Industry alignment
            - Skills relevance
            - Location match (if specified)
            - Company type/size alignment
            
            Provide a score and detailed reasoning.
            """,
            expected_output="A relevance score (0.0-1.0) with detailed keyword matching analysis",
            agent=self.relevance_scorer
        )
        
        # Seniority Assessment Task
        seniority_task = Task(
            description=f"""
            Assess the seniority and decision-making level of this professional:
            
            {profile_data}
            
            Score seniority from 0.0 to 1.0 based on:
            - Job title indicating seniority (C-level, VP, Director, Manager, etc.)
            - Years of experience
            - Company size and role scope
            - Leadership indicators in experience
            - Decision-making authority implied by role
            
            Consider that higher seniority typically means better lead quality.
            Provide a score and reasoning.
            """,
            expected_output="A seniority score (0.0-1.0) with analysis of leadership level",
            agent=self.seniority_assessor
        )
        
        # Final Qualification Task
        qualification_task = Task(
            description=f"""
            Based on the previous analyses, provide a final lead qualification score.
            
            Use these weights:
            - Profile Completeness: {self.criteria.profile_completeness_weight}
            - Relevance: {self.criteria.relevance_weight}
            - Seniority: {self.criteria.seniority_weight}
            - Company Fit: {self.criteria.company_fit_weight}
            - Engagement Potential: {self.criteria.engagement_potential_weight}
            
            Provide:
            1. Overall score (0.0-1.0)
            2. Breakdown by criteria
            3. Reasoning for the score
            4. 3-5 specific recommendations for engagement
            
            Format as JSON with keys: overall_score, criteria_breakdown, reasoning, recommendations
            """,
            expected_output="JSON formatted final scoring result with all components",
            agent=self.lead_qualifier
        )
        
        return [profile_task, relevance_task, seniority_task, qualification_task]

    def _format_profile_for_analysis(self, profile: LeadProfile) -> str:
        """Format profile data for AI analysis"""
        return f"""
        Name: {profile.full_name}
        Headline: {profile.headline}
        Location: {profile.location}
        Company: {profile.company} ({profile.company_size})
        Industry: {profile.industry}
        Experience: {profile.experience_years} years
        Skills: {', '.join(profile.skills[:10])}  # Top 10 skills
        Education: {len(profile.education)} entries
        Connections: {profile.connections_count}
        LinkedIn URL: {profile.linkedin_url}
        """

    def _parse_scoring_result(self, crew_result: str) -> ScoringResult:
        """Parse the crew result into a structured scoring result"""
        try:
            # Try to extract JSON from the result
            result_text = str(crew_result)
            
            # Look for JSON in the result
            start_idx = result_text.find('{')
            end_idx = result_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = result_text[start_idx:end_idx]
                parsed_result = json.loads(json_str)
                
                return ScoringResult(
                    overall_score=float(parsed_result.get('overall_score', 0.5)),
                    criteria_breakdown=parsed_result.get('criteria_breakdown', {}),
                    reasoning=parsed_result.get('reasoning', 'AI analysis completed'),
                    recommendations=parsed_result.get('recommendations', [])
                )
            else:
                # Fallback parsing if JSON not found
                return self._fallback_parse(result_text)
                
        except Exception as e:
            logger.warning(f"Error parsing scoring result: {e}")
            return self._fallback_parse(str(crew_result))

    def _fallback_parse(self, result_text: str) -> ScoringResult:
        """Fallback parsing when JSON extraction fails"""
        # Simple heuristic scoring based on text analysis
        score = 0.5  # Default middle score
        
        # Look for score indicators in text
        if "excellent" in result_text.lower() or "high quality" in result_text.lower():
            score = 0.8
        elif "good" in result_text.lower() or "qualified" in result_text.lower():
            score = 0.7
        elif "poor" in result_text.lower() or "low quality" in result_text.lower():
            score = 0.3
            
        return ScoringResult(
            overall_score=score,
            criteria_breakdown={
                "profile_completeness": score,
                "relevance": score,
                "seniority": score,
                "company_fit": score,
                "engagement_potential": score
            },
            reasoning=result_text[:500],  # First 500 chars
            recommendations=["Manual review recommended"]
        )

    async def batch_score_leads(
        self, 
        profiles: List[LeadProfile],
        target_keywords: List[str],
        target_criteria: Dict[str, Any],
        batch_size: int = 5
    ) -> List[ScoringResult]:
        """Score multiple leads in batches"""
        results = []
        
        for i in range(0, len(profiles), batch_size):
            batch = profiles[i:i + batch_size]
            batch_results = []
            
            for profile in batch:
                result = await self.score_lead(profile, target_keywords, target_criteria)
                batch_results.append(result)
                
            results.extend(batch_results)
            logger.info(f"Completed batch {i//batch_size + 1}, scored {len(batch_results)} leads")
            
        return results
