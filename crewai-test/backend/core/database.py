# Database Configuration and Session Management
# SQLAlchemy setup with connection pooling and session management

from sqlalchemy import create_engine, MetaData, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from typing import Generator
import logging

from core.config import settings

logger = logging.getLogger(__name__)

# Database engine configuration
engine = create_engine(
    settings.database_url_sync,
    poolclass=QueuePool,
    pool_size=settings.DATABASE_POOL_SIZE,
    max_overflow=settings.DATABASE_MAX_OVERFLOW,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=3600,   # Recycle connections every hour
    echo=settings.DEBUG,  # Log SQL queries in debug mode
)

# Session factory
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# Base class for all database models
Base = declarative_base()

# Metadata for database introspection
metadata = MetaData()

class DatabaseManager:
    """
    Database manager class for handling connections and sessions
    """
    
    def __init__(self):
        self.engine = engine
        self.SessionLocal = SessionLocal
    
    def get_session(self) -> Session:
        """
        Get a new database session
        
        Returns:
            Session: SQLAlchemy database session
        """
        return self.SessionLocal()
    
    def create_tables(self):
        """Create all database tables"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Error creating database tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables (use with caution!)"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.warning("All database tables dropped")
        except Exception as e:
            logger.error(f"Error dropping database tables: {e}")
            raise
    
    def check_connection(self) -> bool:
        """
        Check if database connection is working
        
        Returns:
            bool: True if connection is working, False otherwise
        """
        try:
            with self.engine.connect() as connection:
                connection.execute("SELECT 1")
            return True
        except Exception as e:
            logger.error(f"Database connection check failed: {e}")
            return False

# Global database manager instance
db_manager = DatabaseManager()

def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get database session
    
    This function is used as a FastAPI dependency to provide
    database sessions to route handlers.
    
    Yields:
        Session: SQLAlchemy database session
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def get_db_session() -> Session:
    """
    Get a database session for use outside of FastAPI dependencies
    
    Returns:
        Session: SQLAlchemy database session
        
    Note:
        Remember to close the session when done:
        session = get_db_session()
        try:
            # Use session
            pass
        finally:
            session.close()
    """
    return SessionLocal()

# Database event listeners for logging and monitoring
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    """Set database connection parameters"""
    if settings.DEBUG:
        logger.debug("New database connection established")

@event.listens_for(engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log when a connection is checked out from the pool"""
    if settings.DEBUG:
        logger.debug("Connection checked out from pool")

@event.listens_for(engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Log when a connection is returned to the pool"""
    if settings.DEBUG:
        logger.debug("Connection returned to pool")

# Database utilities
class DatabaseUtils:
    """Utility functions for database operations"""
    
    @staticmethod
    def execute_raw_sql(sql: str, params: dict = None) -> list:
        """
        Execute raw SQL query
        
        Args:
            sql: SQL query string
            params: Query parameters
            
        Returns:
            list: Query results
        """
        try:
            with engine.connect() as connection:
                result = connection.execute(sql, params or {})
                return result.fetchall()
        except Exception as e:
            logger.error(f"Raw SQL execution error: {e}")
            raise
    
    @staticmethod
    def get_table_info(table_name: str) -> dict:
        """
        Get information about a database table
        
        Args:
            table_name: Name of the table
            
        Returns:
            dict: Table information
        """
        try:
            with engine.connect() as connection:
                # Get column information
                result = connection.execute(f"""
                    SELECT column_name, data_type, is_nullable, column_default
                    FROM information_schema.columns
                    WHERE table_name = '{table_name}'
                    ORDER BY ordinal_position
                """)
                
                columns = []
                for row in result:
                    columns.append({
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == "YES",
                        "default": row[3]
                    })
                
                return {
                    "table_name": table_name,
                    "columns": columns
                }
        except Exception as e:
            logger.error(f"Error getting table info: {e}")
            return {}
    
    @staticmethod
    def get_database_stats() -> dict:
        """
        Get database statistics
        
        Returns:
            dict: Database statistics
        """
        try:
            stats = {}
            
            # Get connection pool stats
            pool = engine.pool
            stats["pool_size"] = pool.size()
            stats["checked_out_connections"] = pool.checkedout()
            stats["overflow_connections"] = pool.overflow()
            stats["invalid_connections"] = pool.invalidated()
            
            # Get table counts (example for common tables)
            with engine.connect() as connection:
                tables = ["users", "campaigns", "leads"]
                table_counts = {}
                
                for table in tables:
                    try:
                        result = connection.execute(f"SELECT COUNT(*) FROM {table}")
                        count = result.scalar()
                        table_counts[table] = count
                    except:
                        table_counts[table] = "N/A"
                
                stats["table_counts"] = table_counts
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database stats: {e}")
            return {"error": str(e)}

# Create global database utils instance
db_utils = DatabaseUtils()

# Health check function
async def check_database_health() -> dict:
    """
    Check database health for health endpoints
    
    Returns:
        dict: Database health status
    """
    try:
        # Test connection
        is_connected = db_manager.check_connection()
        
        if not is_connected:
            return {
                "status": "unhealthy",
                "message": "Database connection failed"
            }
        
        # Get basic stats
        stats = db_utils.get_database_stats()
        
        return {
            "status": "healthy",
            "connection": "active",
            "pool_stats": {
                "size": stats.get("pool_size", 0),
                "checked_out": stats.get("checked_out_connections", 0),
                "overflow": stats.get("overflow_connections", 0)
            }
        }
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return {
            "status": "unhealthy",
            "message": str(e)
        }

# Initialize database on module import
def init_database():
    """Initialize database connection and verify setup"""
    try:
        # Test connection
        if not db_manager.check_connection():
            raise Exception("Failed to connect to database")
        
        logger.info("✅ Database connection established successfully")
        
        # Create tables if they don't exist
        db_manager.create_tables()
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        raise

# Initialize database when module is imported
if settings.ENVIRONMENT != "test":
    init_database()
