# Security Module
# JWT authentication, password hashing, and security utilities

from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JW<PERSON>rror, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
import logging

from core.config import settings
from core.database import get_db
from models.database import User

logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class SecurityManager:
    """
    Centralized security management class
    Handles JWT tokens, password hashing, and user authentication
    """
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        self.access_token_expire_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        self.refresh_token_expire_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """
        Verify a plain password against its hash
        
        Args:
            plain_password: Plain text password
            hashed_password: Hashed password from database
            
        Returns:
            bool: True if password matches, False otherwise
        """
        try:
            return pwd_context.verify(plain_password, hashed_password)
        except Exception as e:
            logger.error(f"Password verification error: {e}")
            return False
    
    def get_password_hash(self, password: str) -> str:
        """
        Hash a plain password
        
        Args:
            password: Plain text password
            
        Returns:
            str: Hashed password
        """
        return pwd_context.hash(password)
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None) -> str:
        """
        Create a JWT access token
        
        Args:
            data: Data to encode in the token
            expires_delta: Custom expiration time
            
        Returns:
            str: Encoded JWT token
        """
        to_encode = data.copy()
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({
            "exp": expire,
            "type": "access",
            "iat": datetime.utcnow()
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def create_refresh_token(self, data: dict) -> str:
        """
        Create a JWT refresh token
        
        Args:
            data: Data to encode in the token
            
        Returns:
            str: Encoded JWT refresh token
        """
        to_encode = data.copy()
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        to_encode.update({
            "exp": expire,
            "type": "refresh",
            "iat": datetime.utcnow()
        })
        
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str, token_type: str = "access") -> Optional[dict]:
        """
        Verify and decode a JWT token
        
        Args:
            token: JWT token to verify
            token_type: Type of token (access or refresh)
            
        Returns:
            dict: Decoded token payload or None if invalid
        """
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            # Check token type
            if payload.get("type") != token_type:
                logger.warning(f"Invalid token type. Expected: {token_type}, Got: {payload.get('type')}")
                return None
            
            # Check expiration
            exp = payload.get("exp")
            if exp and datetime.fromtimestamp(exp) < datetime.utcnow():
                logger.warning("Token has expired")
                return None
            
            return payload
            
        except JWTError as e:
            logger.error(f"JWT verification error: {e}")
            return None
        except Exception as e:
            logger.error(f"Token verification error: {e}")
            return None
    
    def authenticate_user(self, db: Session, email: str, password: str) -> Optional[User]:
        """
        Authenticate a user with email and password
        
        Args:
            db: Database session
            email: User email
            password: Plain text password
            
        Returns:
            User: Authenticated user or None if authentication fails
        """
        try:
            # Get user by email
            user = db.query(User).filter(User.email == email).first()
            
            if not user:
                logger.warning(f"User not found: {email}")
                return None
            
            if not user.is_active:
                logger.warning(f"Inactive user attempted login: {email}")
                return None
            
            # Verify password
            if not self.verify_password(password, user.password_hash):
                logger.warning(f"Invalid password for user: {email}")
                return None
            
            logger.info(f"User authenticated successfully: {email}")
            return user
            
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return None
    
    def create_user_tokens(self, user: User) -> dict:
        """
        Create access and refresh tokens for a user
        
        Args:
            user: User object
            
        Returns:
            dict: Dictionary containing access and refresh tokens
        """
        # Create token data
        token_data = {
            "sub": str(user.id),
            "email": user.email,
            "user_id": str(user.id)
        }
        
        # Create tokens
        access_token = self.create_access_token(token_data)
        refresh_token = self.create_refresh_token({"sub": str(user.id)})
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": self.access_token_expire_minutes * 60
        }

# Create global security manager instance
security_manager = SecurityManager()

# Convenience functions for backward compatibility
def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password using global security manager"""
    return security_manager.verify_password(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash password using global security manager"""
    return security_manager.get_password_hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create access token using global security manager"""
    return security_manager.create_access_token(data, expires_delta)

def create_refresh_token(data: dict) -> str:
    """Create refresh token using global security manager"""
    return security_manager.create_refresh_token(data)

async def verify_token(token: str) -> Optional[User]:
    """
    Verify JWT token and return user
    
    Args:
        token: JWT token to verify
        
    Returns:
        User: User object if token is valid, None otherwise
    """
    try:
        # Verify token
        payload = security_manager.verify_token(token)
        if not payload:
            return None
        
        # Get user ID from token
        user_id = payload.get("sub")
        if not user_id:
            logger.warning("Token missing user ID")
            return None
        
        # Get user from database
        db = next(get_db())
        user = db.query(User).filter(User.id == user_id).first()
        
        if not user:
            logger.warning(f"User not found for token: {user_id}")
            return None
        
        if not user.is_active:
            logger.warning(f"Inactive user in token: {user_id}")
            return None
        
        return user
        
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        return None

def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """Authenticate user using global security manager"""
    return security_manager.authenticate_user(db, email, password)

def create_user_tokens(user: User) -> dict:
    """Create user tokens using global security manager"""
    return security_manager.create_user_tokens(user)

# Password validation utilities
def validate_password_strength(password: str) -> tuple[bool, str]:
    """
    Validate password strength
    
    Args:
        password: Password to validate
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not any(c.isupper() for c in password):
        return False, "Password must contain at least one uppercase letter"
    
    if not any(c.islower() for c in password):
        return False, "Password must contain at least one lowercase letter"
    
    if not any(c.isdigit() for c in password):
        return False, "Password must contain at least one digit"
    
    special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
    if not any(c in special_chars for c in password):
        return False, "Password must contain at least one special character"
    
    return True, "Password is strong"

# Rate limiting utilities
def check_rate_limit(user_id: str, action: str) -> bool:
    """
    Check if user has exceeded rate limit for a specific action
    
    Args:
        user_id: User ID
        action: Action being performed
        
    Returns:
        bool: True if within rate limit, False if exceeded
    """
    # TODO: Implement Redis-based rate limiting
    # This is a placeholder implementation
    return True
