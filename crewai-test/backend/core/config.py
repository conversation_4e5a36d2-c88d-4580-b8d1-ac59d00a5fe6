# Configuration Settings
# Centralized configuration management using Pydantic Settings

from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import List, Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    """
    Application settings with environment variable support
    All settings can be overridden via environment variables
    """
    
    # Application Settings
    APP_NAME: str = "LinkedIn Lead Generation API"
    VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, description="Debug mode")
    ENVIRONMENT: str = Field(default="development", description="Environment (development, staging, production)")
    
    # Security Settings
    SECRET_KEY: str = Field(..., description="Secret key for JWT token generation")
    ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="Access token expiration in minutes")
    REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="Refresh token expiration in days")
    
    # Database Settings
    DATABASE_URL: str = Field(..., description="PostgreSQL database URL")
    DATABASE_POOL_SIZE: int = Field(default=10, description="Database connection pool size")
    DATABASE_MAX_OVERFLOW: int = Field(default=20, description="Database max overflow connections")
    
    # Redis Settings
    REDIS_URL: str = Field(default="redis://localhost:6379", description="Redis URL for caching")
    REDIS_EXPIRE_TIME: int = Field(default=3600, description="Redis cache expiration time in seconds")
    
    # External API Settings
    OPENAI_API_KEY: str = Field(..., description="OpenAI API key for AI scoring")
    APIFY_API_TOKEN: str = Field(..., description="Apify API token for LinkedIn scraping")
    APIFY_ACTOR_ID: str = Field(default="apify/linkedin-profile-scraper", description="Apify actor ID")
    
    # CORS Settings
    ALLOWED_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="Allowed CORS origins"
    )
    ALLOWED_HOSTS: List[str] = Field(
        default=["localhost", "127.0.0.1"],
        description="Allowed hosts"
    )
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = Field(default=60, description="API rate limit per minute per user")
    
    # File Upload Settings
    MAX_FILE_SIZE: int = Field(default=10 * 1024 * 1024, description="Max file upload size in bytes (10MB)")
    UPLOAD_DIR: str = Field(default="uploads", description="Directory for file uploads")
    
    # Email Settings (for notifications)
    SMTP_HOST: Optional[str] = Field(default=None, description="SMTP host for email notifications")
    SMTP_PORT: int = Field(default=587, description="SMTP port")
    SMTP_USERNAME: Optional[str] = Field(default=None, description="SMTP username")
    SMTP_PASSWORD: Optional[str] = Field(default=None, description="SMTP password")
    SMTP_USE_TLS: bool = Field(default=True, description="Use TLS for SMTP")
    
    # Logging Settings
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FILE: Optional[str] = Field(default=None, description="Log file path")
    
    # Background Task Settings
    CELERY_BROKER_URL: Optional[str] = Field(default=None, description="Celery broker URL")
    CELERY_RESULT_BACKEND: Optional[str] = Field(default=None, description="Celery result backend")
    
    # Scraping Settings
    MAX_CONCURRENT_SCRAPING_JOBS: int = Field(default=5, description="Max concurrent scraping jobs")
    SCRAPING_TIMEOUT_MINUTES: int = Field(default=30, description="Scraping timeout in minutes")
    
    # AI Scoring Settings
    AI_SCORING_BATCH_SIZE: int = Field(default=10, description="Batch size for AI scoring")
    AI_SCORING_TIMEOUT_SECONDS: int = Field(default=300, description="AI scoring timeout in seconds")
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        """Validate environment setting"""
        allowed_envs = ["development", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"Environment must be one of: {allowed_envs}")
        return v
    
    @validator("DATABASE_URL")
    def validate_database_url(cls, v):
        """Validate database URL format"""
        if not v.startswith(("postgresql://", "postgresql+psycopg2://")):
            raise ValueError("DATABASE_URL must be a valid PostgreSQL URL")
        return v
    
    @validator("REDIS_URL")
    def validate_redis_url(cls, v):
        """Validate Redis URL format"""
        if not v.startswith("redis://"):
            raise ValueError("REDIS_URL must be a valid Redis URL")
        return v
    
    @property
    def database_url_sync(self) -> str:
        """Get synchronous database URL for SQLAlchemy"""
        return self.DATABASE_URL.replace("postgresql://", "postgresql+psycopg2://")
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.ENVIRONMENT == "development"
    
    def get_upload_path(self, filename: str) -> Path:
        """Get full upload path for a file"""
        upload_dir = Path(self.UPLOAD_DIR)
        upload_dir.mkdir(exist_ok=True)
        return upload_dir / filename
    
    class Config:
        """Pydantic configuration"""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        
        # Example environment variables
        env_prefix = ""
        
        # Schema extra for documentation
        schema_extra = {
            "example": {
                "SECRET_KEY": "your-secret-key-here",
                "DATABASE_URL": "postgresql://user:password@localhost/linkedin_leads",
                "OPENAI_API_KEY": "sk-...",
                "APIFY_API_TOKEN": "apify_api_...",
                "DEBUG": False,
                "ENVIRONMENT": "production"
            }
        }

# Create global settings instance
settings = Settings()

# Validate critical settings on startup
def validate_settings():
    """Validate critical settings on application startup"""
    required_settings = [
        "SECRET_KEY",
        "DATABASE_URL", 
        "OPENAI_API_KEY",
        "APIFY_API_TOKEN"
    ]
    
    missing_settings = []
    for setting in required_settings:
        if not getattr(settings, setting, None):
            missing_settings.append(setting)
    
    if missing_settings:
        raise ValueError(f"Missing required settings: {', '.join(missing_settings)}")
    
    print(f"✅ Configuration validated for {settings.ENVIRONMENT} environment")

# Validate settings on import
validate_settings()
