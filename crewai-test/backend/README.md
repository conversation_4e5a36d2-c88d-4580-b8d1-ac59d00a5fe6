# LinkedIn Lead Generation API

A comprehensive FastAPI backend service for LinkedIn lead generation with AI-powered scoring, built with modern Python technologies.

## 🚀 Features

- **JWT Authentication** - Secure user authentication with access and refresh tokens
- **Campaign Management** - Create and manage LinkedIn lead generation campaigns
- **Apify Integration** - Automated LinkedIn data scraping using Apify actors
- **AI-Powered Scoring** - Intelligent lead qualification using CrewAI and OpenAI
- **Advanced Filtering** - Comprehensive lead filtering and search capabilities
- **Real-time Updates** - WebSocket support for live campaign status
- **Export Functionality** - CSV, Excel, and JSON export capabilities
- **Rate Limiting** - Built-in API rate limiting and security
- **Comprehensive Logging** - Structured logging with monitoring support

## 🛠️ Tech Stack

- **FastAPI** - Modern, fast web framework for building APIs
- **PostgreSQL** - Robust relational database with JSON support
- **Redis** - In-memory caching and session storage
- **SQLAlchemy** - Python SQL toolkit and ORM
- **Pydantic** - Data validation using Python type annotations
- **CrewAI** - Multi-agent AI framework for lead scoring
- **Apify** - Web scraping platform for LinkedIn data
- **Docker** - Containerization for easy deployment

## 📋 Prerequisites

- Python 3.10+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose (optional)

## 🔧 Installation

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

5. **Set up database**
   ```bash
   # Create PostgreSQL database
   createdb linkedin_leads
   
   # Run migrations (if using Alembic)
   alembic upgrade head
   ```

6. **Start the application**
   ```bash
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

### Docker Setup

1. **Using Docker Compose**
   ```bash
   # Copy environment file
   cp .env.example .env
   # Edit .env with your values
   
   # Start all services
   docker-compose up -d
   
   # View logs
   docker-compose logs -f api
   ```

2. **Build and run manually**
   ```bash
   # Build image
   docker build -t linkedin-leads-api .
   
   # Run container
   docker run -p 8000:8000 --env-file .env linkedin-leads-api
   ```

## 📚 API Documentation

Once the application is running, visit:

- **Interactive API Docs**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **OpenAPI Schema**: http://localhost:8000/openapi.json

## 🔐 Authentication

The API uses JWT (JSON Web Tokens) for authentication:

1. **Register a new user**
   ```bash
   POST /api/v1/auth/register
   ```

2. **Login to get tokens**
   ```bash
   POST /api/v1/auth/login
   ```

3. **Use access token in headers**
   ```bash
   Authorization: Bearer <your-access-token>
   ```

## 📊 API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/refresh` - Refresh access token
- `GET /api/v1/auth/me` - Get current user profile

### Campaigns
- `GET /api/v1/campaigns` - List campaigns
- `POST /api/v1/campaigns` - Create campaign
- `GET /api/v1/campaigns/{id}` - Get campaign details
- `PUT /api/v1/campaigns/{id}` - Update campaign
- `POST /api/v1/campaigns/{id}/start` - Start campaign

### Leads
- `GET /api/v1/leads` - List leads with filtering
- `GET /api/v1/leads/{id}` - Get lead details
- `PUT /api/v1/leads/{id}` - Update lead
- `POST /api/v1/leads/bulk-action` - Bulk operations

### Scraping
- `POST /api/v1/scraping/start` - Start LinkedIn scraping
- `GET /api/v1/scraping/status/{campaign_id}` - Get scraping status
- `POST /api/v1/scraping/stop/{campaign_id}` - Stop scraping

## 🔧 Configuration

Key environment variables:

```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost/linkedin_leads

# API Keys
OPENAI_API_KEY=sk-your-openai-key
APIFY_API_TOKEN=apify_api_your-token

# Security
SECRET_KEY=your-secret-key
JWT_ALGORITHM=HS256

# Redis
REDIS_URL=redis://localhost:6379/0
```

## 🧪 Testing

Run the test suite:

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html
```

## 📝 Database Schema

The application uses the following main tables:

- **users** - User accounts and authentication
- **campaigns** - Lead generation campaigns
- **leads** - LinkedIn profile data and AI scores
- **scoring_history** - AI scoring audit trail
- **exports** - Data export jobs

## 🚀 Deployment

### Production Deployment

1. **Set environment to production**
   ```bash
   ENVIRONMENT=production
   DEBUG=False
   ```

2. **Use production database**
   ```bash
   DATABASE_URL=***********************************/linkedin_leads
   ```

3. **Configure reverse proxy** (Nginx example)
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       
       location / {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

### Scaling Considerations

- Use multiple worker processes: `uvicorn main:app --workers 4`
- Implement Redis clustering for high availability
- Use database connection pooling
- Add load balancer for multiple API instances

## 📊 Monitoring

The application includes:

- Health check endpoints (`/health`)
- Structured logging with correlation IDs
- Performance metrics collection
- Error tracking with Sentry integration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Check the API documentation at `/docs`
- Review the logs in `logs/app.log`
- Open an issue on GitHub
- Contact the development team

## 🔄 Changelog

### v1.0.0
- Initial release with core functionality
- JWT authentication system
- Campaign and lead management
- Apify integration for LinkedIn scraping
- AI-powered lead scoring with CrewAI
- Export functionality
- Docker support
