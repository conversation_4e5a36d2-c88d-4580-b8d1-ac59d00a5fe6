# FastAPI LinkedIn Lead Generation Backend Requirements

# Core FastAPI and ASGI
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
alembic==1.12.1

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Environment and Configuration
pydantic==2.5.0
pydantic-settings==2.1.0
python-dotenv==1.0.0

# HTTP Client and API Integration
httpx==0.25.2
aiohttp==3.9.1
requests==2.31.0

# Redis for Caching
redis==5.0.1
aioredis==2.0.1

# AI and Machine Learning
openai==1.3.8
langchain==0.0.350
langchain-openai==0.0.2
crewai==0.22.5

# Apify Integration
apify-client==1.7.1

# Background Tasks and Job Queue
celery==5.3.4
kombu==5.3.4

# Data Processing and Validation
pandas==2.1.4
numpy==1.25.2
openpyxl==3.1.2

# Logging and Monitoring
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
pytest-mock==3.12.0

# Development Tools
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Email Support
fastapi-mail==1.4.1

# File Upload and Storage
python-magic==0.4.27
boto3==1.34.0  # For AWS S3 integration

# Rate Limiting
slowapi==0.1.9

# CORS and Security Headers
python-cors==1.7.0

# Date and Time Utilities
python-dateutil==2.8.2
pytz==2023.3

# JSON and Data Serialization
orjson==3.9.10

# WebSocket Support (for real-time updates)
websockets==12.0

# Health Checks
psutil==5.9.6

# Cryptography
cryptography==41.0.8

# UUID utilities
uuid==1.30

# Type hints
typing-extensions==4.8.0
