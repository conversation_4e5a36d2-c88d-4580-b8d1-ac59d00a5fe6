#!/usr/bin/env python3
"""
Database Integration Example
Demonstrates how to save Apify LinkedIn scraping results to both PostgreSQL and MongoDB
"""

import asyncio
import os
import sys
from datetime import datetime
from typing import List
import uuid

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.apify_client import ApifyClient, LinkedInSearchInput, LinkedInProfile
from services.lead_data_processor import LeadDataProcessor
from services.mongodb_lead_processor import MongoLeadDataProcessor
from models.database import Campaign, Lead, User
from core.database import get_db
from sqlalchemy.orm import Session

async def create_sample_linkedin_profiles() -> List[LinkedInProfile]:
    """Create sample LinkedIn profiles for testing"""
    
    sample_profiles = [
        LinkedInProfile(
            url="https://linkedin.com/in/john-doe-engineer",
            full_name="<PERSON>",
            headline="Senior Software Engineer at TechCorp",
            location="San Francisco, CA, United States",
            summary="Experienced software engineer with 8+ years in full-stack development...",
            company="TechCorp",
            company_url="https://techcorp.com",
            company_size="201-500",
            industry="Technology",
            current_position="Senior Software Engineer",
            experience=[
                {
                    "title": "Senior Software Engineer",
                    "company": "TechCorp",
                    "startDate": "2020-01",
                    "endDate": "Present",
                    "description": "Lead development of microservices architecture..."
                },
                {
                    "title": "Software Engineer",
                    "company": "StartupXYZ",
                    "startDate": "2018-06",
                    "endDate": "2019-12",
                    "description": "Developed React applications and REST APIs..."
                }
            ],
            education=[
                {
                    "school": "Stanford University",
                    "degree": "Bachelor of Science",
                    "field": "Computer Science",
                    "startDate": "2014",
                    "endDate": "2018"
                }
            ],
            skills=["Python", "JavaScript", "React", "Node.js", "PostgreSQL", "AWS"],
            languages=["English", "Spanish"],
            connections_count=500,
            followers_count=1200,
            profile_image_url="https://media.licdn.com/dms/image/...",
            contact_info={
                "email": "<EMAIL>",
                "website": "https://johndoe.dev"
            }
        ),
        
        LinkedInProfile(
            url="https://linkedin.com/in/jane-smith-data",
            full_name="Jane Smith",
            headline="Data Scientist | Machine Learning Engineer",
            location="New York, NY, United States",
            summary="Passionate data scientist with expertise in ML and AI...",
            company="DataCorp",
            company_url="https://datacorp.com",
            company_size="1001-5000",
            industry="Technology",
            current_position="Senior Data Scientist",
            experience=[
                {
                    "title": "Senior Data Scientist",
                    "company": "DataCorp",
                    "startDate": "2021-03",
                    "endDate": "Present",
                    "description": "Building ML models for predictive analytics..."
                }
            ],
            education=[
                {
                    "school": "MIT",
                    "degree": "Master of Science",
                    "field": "Data Science",
                    "startDate": "2018",
                    "endDate": "2020"
                }
            ],
            skills=["Python", "TensorFlow", "PyTorch", "SQL", "R", "Tableau"],
            languages=["English", "French"],
            connections_count=800,
            followers_count=2500,
            profile_image_url="https://media.licdn.com/dms/image/...",
            contact_info={
                "email": "<EMAIL>"
            }
        ),
        
        LinkedInProfile(
            url="https://linkedin.com/in/mike-johnson-product",
            full_name="Mike Johnson",
            headline="Product Manager | SaaS | B2B",
            location="Austin, TX, United States",
            summary="Product manager with 6+ years experience in SaaS products...",
            company="SaaS Solutions Inc",
            company_size="51-200",
            industry="Software",
            current_position="Senior Product Manager",
            experience=[
                {
                    "title": "Senior Product Manager",
                    "company": "SaaS Solutions Inc",
                    "startDate": "2020-08",
                    "endDate": "Present",
                    "description": "Leading product strategy for B2B platform..."
                }
            ],
            education=[
                {
                    "school": "University of Texas",
                    "degree": "MBA",
                    "field": "Business Administration",
                    "startDate": "2016",
                    "endDate": "2018"
                }
            ],
            skills=["Product Management", "Agile", "Scrum", "Analytics", "SQL"],
            languages=["English"],
            connections_count=1500,
            followers_count=800,
            contact_info={}
        )
    ]
    
    return sample_profiles

async def postgresql_example():
    """Example of saving leads to PostgreSQL"""
    print("🐘 PostgreSQL Integration Example")
    print("=" * 50)
    
    try:
        # Get database session
        db = next(get_db())
        
        # Create or get a test user
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not test_user:
            test_user = User(
                email="<EMAIL>",
                password_hash="$2b$12$test_hash",
                first_name="Test",
                last_name="User",
                subscription_tier="premium",
                credits_remaining=1000,
                is_active=True
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
            print(f"✅ Created test user: {test_user.email}")
        else:
            print(f"✅ Using existing test user: {test_user.email}")
        
        # Create or get a test campaign
        test_campaign = db.query(Campaign).filter(
            Campaign.user_id == test_user.id,
            Campaign.name == "Test Campaign - Database Integration"
        ).first()
        
        if not test_campaign:
            test_campaign = Campaign(
                user_id=test_user.id,
                name="Test Campaign - Database Integration",
                description="Test campaign for database integration example",
                keywords=["software engineer", "data scientist", "product manager"],
                target_locations=["San Francisco", "New York", "Austin"],
                max_leads=100,
                status="active"
            )
            db.add(test_campaign)
            db.commit()
            db.refresh(test_campaign)
            print(f"✅ Created test campaign: {test_campaign.name}")
        else:
            print(f"✅ Using existing test campaign: {test_campaign.name}")
        
        # Create sample LinkedIn profiles
        sample_profiles = await create_sample_linkedin_profiles()
        print(f"📝 Created {len(sample_profiles)} sample LinkedIn profiles")
        
        # Process profiles using LeadDataProcessor
        processor = LeadDataProcessor()
        stats = await processor.process_apify_results(
            campaign_id=test_campaign.id,
            apify_profiles=sample_profiles,
            db=db
        )
        
        print(f"📊 Processing Results:")
        print(f"   Total profiles: {stats['total_profiles']}")
        print(f"   Leads created: {stats['leads_created']}")
        print(f"   Leads updated: {stats['leads_updated']}")
        print(f"   Duplicates found: {stats['duplicates_found']}")
        print(f"   Validation failures: {stats['validation_failures']}")
        print(f"   Processing errors: {stats['processing_errors']}")
        
        # Query and display saved leads
        leads = db.query(Lead).filter(Lead.campaign_id == test_campaign.id).all()
        print(f"\n👥 Saved Leads ({len(leads)}):")
        print("-" * 30)
        
        for lead in leads:
            print(f"• {lead.full_name}")
            print(f"  Company: {lead.company}")
            print(f"  Location: {lead.location}")
            print(f"  Data Quality: {lead.data_quality_score}")
            print(f"  LinkedIn: {lead.linkedin_url}")
            print()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ PostgreSQL example failed: {e}")
        return False

async def mongodb_example():
    """Example of saving leads to MongoDB"""
    print("\n🍃 MongoDB Integration Example")
    print("=" * 50)
    
    try:
        # Initialize MongoDB processor
        mongo_processor = MongoLeadDataProcessor()
        mongo_processor.connect()
        
        # Create test user in MongoDB
        test_user_doc = {
            "email": "<EMAIL>",
            "password_hash": "$2b$12$test_hash",
            "first_name": "Test",
            "last_name": "User",
            "subscription_tier": "premium",
            "credits_remaining": 1000,
            "credits_used": 0,
            "is_active": True,
            "is_verified": True,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Insert or get user
        existing_user = mongo_processor.db.users.find_one({"email": "<EMAIL>"})
        if not existing_user:
            user_result = mongo_processor.db.users.insert_one(test_user_doc)
            user_id = user_result.inserted_id
            print(f"✅ Created test user in MongoDB")
        else:
            user_id = existing_user["_id"]
            print(f"✅ Using existing test user in MongoDB")
        
        # Create test campaign in MongoDB
        test_campaign_doc = {
            "user_id": user_id,
            "name": "Test Campaign - MongoDB Integration",
            "description": "Test campaign for MongoDB integration example",
            "keywords": ["software engineer", "data scientist", "product manager"],
            "target_locations": ["San Francisco", "New York", "Austin"],
            "target_industries": ["Technology", "Software"],
            "max_leads": 100,
            "status": "active",
            "leads_found": 0,
            "leads_processed": 0,
            "leads_qualified": 0,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        # Insert or get campaign
        existing_campaign = mongo_processor.db.campaigns.find_one({
            "user_id": user_id,
            "name": "Test Campaign - MongoDB Integration"
        })
        
        if not existing_campaign:
            campaign_result = mongo_processor.db.campaigns.insert_one(test_campaign_doc)
            campaign_id = str(campaign_result.inserted_id)
            print(f"✅ Created test campaign in MongoDB")
        else:
            campaign_id = str(existing_campaign["_id"])
            print(f"✅ Using existing test campaign in MongoDB")
        
        # Create sample LinkedIn profiles
        sample_profiles = await create_sample_linkedin_profiles()
        print(f"📝 Created {len(sample_profiles)} sample LinkedIn profiles")
        
        # Process profiles using MongoLeadDataProcessor
        stats = await mongo_processor.process_apify_results(
            campaign_id=campaign_id,
            apify_profiles=sample_profiles
        )
        
        print(f"📊 Processing Results:")
        print(f"   Total profiles: {stats['total_profiles']}")
        print(f"   Leads created: {stats['leads_created']}")
        print(f"   Leads updated: {stats['leads_updated']}")
        print(f"   Duplicates found: {stats['duplicates_found']}")
        print(f"   Validation failures: {stats['validation_failures']}")
        print(f"   Processing errors: {stats['processing_errors']}")
        
        # Query and display saved leads
        from bson import ObjectId
        leads = list(mongo_processor.db.leads.find({"campaign_id": ObjectId(campaign_id)}))
        print(f"\n👥 Saved Leads ({len(leads)}):")
        print("-" * 30)
        
        for lead in leads:
            print(f"• {lead.get('full_name', 'N/A')}")
            print(f"  Company: {lead.get('company', 'N/A')}")
            print(f"  Location: {lead.get('location', 'N/A')}")
            print(f"  Data Quality: {lead.get('data_quality_score', 'N/A')}")
            print(f"  LinkedIn: {lead.get('linkedin_url', 'N/A')}")
            print()
        
        # Get campaign statistics
        campaign_stats = mongo_processor.get_campaign_stats(campaign_id)
        if campaign_stats:
            print(f"📈 Campaign Statistics:")
            print(f"   Total leads: {campaign_stats.get('total_leads', 0)}")
            print(f"   Average data quality: {campaign_stats.get('avg_data_quality', 0):.2f}")
        
        mongo_processor.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB example failed: {e}")
        return False

async def comparison_example():
    """Compare PostgreSQL vs MongoDB performance and features"""
    print("\n⚖️  Database Comparison")
    print("=" * 50)
    
    print("📊 Feature Comparison:")
    print()
    
    comparison_table = [
        ("Feature", "PostgreSQL", "MongoDB"),
        ("Schema", "Rigid (SQL)", "Flexible (JSON)"),
        ("ACID Compliance", "Full ACID", "Document-level"),
        ("Indexing", "B-tree, GIN, etc.", "B-tree, Text, Geo"),
        ("Queries", "SQL", "MongoDB Query Language"),
        ("Joins", "Native SQL joins", "Aggregation pipeline"),
        ("Scalability", "Vertical + Read replicas", "Horizontal sharding"),
        ("JSON Support", "JSONB columns", "Native document store"),
        ("Full-text Search", "Built-in + extensions", "Text indexes"),
        ("Analytics", "SQL aggregations", "Aggregation framework"),
        ("Backup/Recovery", "pg_dump, WAL", "mongodump, replica sets"),
        ("Learning Curve", "SQL knowledge", "NoSQL concepts")
    ]
    
    # Print comparison table
    for row in comparison_table:
        print(f"{row[0]:<20} | {row[1]:<25} | {row[2]:<25}")
        if row[0] == "Feature":
            print("-" * 75)
    
    print("\n💡 Recommendations:")
    print("• Use PostgreSQL for:")
    print("  - Complex relational queries")
    print("  - Strong consistency requirements")
    print("  - Existing SQL expertise")
    print("  - Advanced analytics and reporting")
    print()
    print("• Use MongoDB for:")
    print("  - Flexible, evolving schemas")
    print("  - Rapid prototyping")
    print("  - Horizontal scaling needs")
    print("  - Document-centric applications")

def environment_check():
    """Check database connection requirements"""
    print("🔧 Database Environment Check")
    print("=" * 50)
    
    # Check PostgreSQL requirements
    print("PostgreSQL Requirements:")
    pg_vars = ["DATABASE_URL"]
    for var in pg_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:20]}...")
        else:
            print(f"❌ {var}: Not set")
    
    print("\nMongoDB Requirements:")
    mongo_vars = ["MONGODB_URL"]
    for var in mongo_vars:
        value = os.getenv(var, "mongodb://localhost:27017")
        print(f"✅ {var}: {value[:30]}...")
    
    print("\n📝 Note: Make sure your databases are running and accessible")

async def main():
    """Main example runner"""
    print("🗄️  Database Integration Examples")
    print("=" * 60)
    print()
    
    # Check environment
    environment_check()
    print()
    
    # Ask user what to run
    print("🎮 What would you like to run?")
    print("1. PostgreSQL example")
    print("2. MongoDB example")
    print("3. Both examples")
    print("4. Database comparison")
    print("5. Exit")
    
    try:
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            await postgresql_example()
        elif choice == "2":
            await mongodb_example()
        elif choice == "3":
            pg_success = await postgresql_example()
            mongo_success = await mongodb_example()
            print(f"\n📊 Results: PostgreSQL: {'✅' if pg_success else '❌'}, MongoDB: {'✅' if mongo_success else '❌'}")
        elif choice == "4":
            await comparison_example()
        elif choice == "5":
            print("Goodbye!")
        else:
            print("Invalid choice.")
            
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    # Load environment variables
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("Note: python-dotenv not installed. Make sure environment variables are set manually.")
    
    # Run the examples
    asyncio.run(main())
