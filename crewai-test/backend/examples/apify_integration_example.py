#!/usr/bin/env python3
"""
Apify LinkedIn Scraper Integration Example
Demonstrates how to use the Apify API for LinkedIn lead scraping
"""

import asyncio
import os
import json
from datetime import datetime
from typing import List

# Add parent directory to path for imports
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.apify_client import ApifyClient, LinkedInSearchInput, ApifyRunStatus
from services.linkedin_scraper import ScrapingJobManager

async def basic_apify_example():
    """
    Basic example of using Apify client directly
    """
    print("🚀 Basic Apify LinkedIn Scraper Example")
    print("=" * 50)
    
    # Initialize Apify client
    # Make sure you have APIFY_API_TOKEN and APIFY_ACTOR_ID in your environment
    try:
        client = ApifyClient()
        print("✅ Apify client initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize Apify client: {e}")
        print("Make sure you have APIFY_API_TOKEN and APIFY_ACTOR_ID environment variables set")
        return
    
    # Define search parameters
    search_input = LinkedInSearchInput(
        keywords=["software engineer", "python developer"],
        locations=["San Francisco", "New York"],
        industries=["Technology", "Software"],
        max_results=50,  # Small number for testing
        include_private_profiles=False,
        include_skills=True,
        include_experience=True,
        use_residential_proxies=True
    )
    
    print(f"🔍 Search Parameters:")
    print(f"   Keywords: {search_input.keywords}")
    print(f"   Locations: {search_input.locations}")
    print(f"   Max Results: {search_input.max_results}")
    print()
    
    try:
        # Start scraping
        print("🏃 Starting LinkedIn scraping...")
        run_id = await client.start_linkedin_scraping(search_input)
        print(f"✅ Scraping started successfully!")
        print(f"   Run ID: {run_id}")
        print()
        
        # Monitor progress
        print("⏳ Monitoring scraping progress...")
        max_checks = 20  # Maximum number of status checks
        check_interval = 15  # seconds between checks
        
        for i in range(max_checks):
            status_result = await client.get_run_status(run_id)
            
            print(f"   Check {i+1}: Status = {status_result.status.value}")
            
            if status_result.stats:
                items_found = status_result.stats.get("itemCount", 0)
                runtime = status_result.stats.get("runTimeSecs", 0)
                print(f"   Items found: {items_found}, Runtime: {runtime}s")
            
            # Check if completed
            if status_result.status == ApifyRunStatus.SUCCEEDED:
                print("🎉 Scraping completed successfully!")
                break
            elif status_result.status in [ApifyRunStatus.FAILED, ApifyRunStatus.ABORTED, ApifyRunStatus.TIMED_OUT]:
                print(f"❌ Scraping failed with status: {status_result.status.value}")
                if status_result.error_message:
                    print(f"   Error: {status_result.error_message}")
                return
            
            # Wait before next check
            if i < max_checks - 1:  # Don't wait after last check
                await asyncio.sleep(check_interval)
        
        # Get results
        if status_result.status == ApifyRunStatus.SUCCEEDED:
            print("\n📥 Fetching results...")
            profiles = await client.get_run_results(run_id, limit=10)  # Get first 10 results
            
            print(f"✅ Retrieved {len(profiles)} LinkedIn profiles")
            print("\n👥 Sample Results:")
            print("-" * 30)
            
            for i, profile in enumerate(profiles[:3], 1):  # Show first 3 profiles
                print(f"{i}. {profile.full_name}")
                print(f"   Headline: {profile.headline}")
                print(f"   Company: {profile.company}")
                print(f"   Location: {profile.location}")
                print(f"   Skills: {', '.join(profile.skills[:5])}...")  # First 5 skills
                print(f"   LinkedIn: {profile.url}")
                print()
        
    except Exception as e:
        print(f"❌ Error during scraping: {e}")

async def scraping_manager_example():
    """
    Example using the high-level ScrapingJobManager
    """
    print("\n🎯 Scraping Manager Example")
    print("=" * 50)
    
    # This example shows how the scraping manager would be used
    # Note: This requires a database setup and campaign data
    
    print("📝 This example demonstrates the ScrapingJobManager interface:")
    print()
    
    # Example usage (pseudo-code since we don't have a real campaign)
    example_code = '''
    # Initialize scraping manager
    manager = ScrapingJobManager()
    
    # Start scraping for a campaign
    run_id = await manager.start_campaign_scraping(
        campaign_id=uuid.UUID("..."),
        user_id=uuid.UUID("...")
    )
    
    # Check job status
    status = await manager.get_job_status(campaign_id)
    print(f"Status: {status['status']}")
    print(f"Progress: {status['progress']}")
    
    # Stop job if needed
    success = await manager.stop_job(campaign_id)
    '''
    
    print(example_code)

def environment_check():
    """
    Check if required environment variables are set
    """
    print("🔧 Environment Check")
    print("=" * 50)
    
    required_vars = [
        "APIFY_API_TOKEN",
        "APIFY_ACTOR_ID",
        "OPENAI_API_KEY",
        "DATABASE_URL"
    ]
    
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # Mask sensitive values
            if "TOKEN" in var or "KEY" in var:
                masked_value = value[:8] + "..." + value[-4:] if len(value) > 12 else "***"
                print(f"✅ {var}: {masked_value}")
            else:
                print(f"✅ {var}: {value}")
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these variables in your .env file or environment")
        return False
    else:
        print("\n✅ All required environment variables are set!")
        return True

async def test_apify_connection():
    """
    Test basic Apify API connection
    """
    print("\n🔌 Testing Apify Connection")
    print("=" * 50)
    
    try:
        client = ApifyClient()
        
        # Test with a minimal search
        test_input = LinkedInSearchInput(
            keywords=["test"],
            max_results=1,
            include_private_profiles=False
        )
        
        print("🧪 Testing Apify actor start...")
        run_id = await client.start_linkedin_scraping(test_input)
        print(f"✅ Successfully started test run: {run_id}")
        
        # Check status once
        print("🔍 Checking run status...")
        status = await client.get_run_status(run_id)
        print(f"✅ Status check successful: {status.status.value}")
        
        # Abort the test run to avoid charges
        print("🛑 Aborting test run...")
        await client.abort_run(run_id)
        print("✅ Test run aborted successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

async def main():
    """
    Main example runner
    """
    print("🔗 Apify LinkedIn Scraper Integration Examples")
    print("=" * 60)
    print()
    
    # Check environment
    if not environment_check():
        print("\n❌ Environment check failed. Please fix the issues above.")
        return
    
    # Test connection
    print()
    connection_ok = await test_apify_connection()
    
    if not connection_ok:
        print("\n❌ Connection test failed. Please check your API credentials.")
        return
    
    # Ask user what to run
    print("\n🎮 What would you like to run?")
    print("1. Basic Apify example (will use credits)")
    print("2. Scraping manager example (documentation)")
    print("3. Exit")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            confirm = input("\n⚠️  This will use Apify credits. Continue? (y/N): ").strip().lower()
            if confirm == 'y':
                await basic_apify_example()
            else:
                print("Cancelled.")
        elif choice == "2":
            await scraping_manager_example()
        elif choice == "3":
            print("Goodbye!")
        else:
            print("Invalid choice.")
            
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    # Load environment variables from .env file if it exists
    try:
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("Note: python-dotenv not installed. Make sure environment variables are set manually.")
    
    # Run the examples
    asyncio.run(main())
