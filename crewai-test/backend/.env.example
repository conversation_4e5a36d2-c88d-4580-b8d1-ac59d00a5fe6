# Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Application Settings
SECRET_KEY=your-super-secret-key-here-change-this-in-production
DEBUG=False
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/linkedin_leads
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_EXPIRE_TIME=3600

# External API Keys
OPENAI_API_KEY=sk-your-openai-api-key-here
APIFY_API_TOKEN=apify_api_your-apify-token-here
APIFY_ACTOR_ID=apify/linkedin-profile-scraper

# JWT Configuration
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Settings
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:8080","https://yourdomain.com"]
ALLOWED_HOSTS=["localhost","127.0.0.1","yourdomain.com"]

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# File Upload Settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Background Tasks (Optional - for Celery)
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# Scraping Configuration
MAX_CONCURRENT_SCRAPING_JOBS=5
SCRAPING_TIMEOUT_MINUTES=30

# AI Scoring Configuration
AI_SCORING_BATCH_SIZE=10
AI_SCORING_TIMEOUT_SECONDS=300

# Monitoring (Optional)
SENTRY_DSN=https://your-sentry-dsn-here

# AWS Configuration (Optional - for file storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name
