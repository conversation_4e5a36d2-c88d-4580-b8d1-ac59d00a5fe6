# Apify LinkedIn Scraper Integration Guide

This guide shows how to integrate Apify's LinkedIn scraper actors into your FastAPI backend for automated lead generation.

## 🔧 Setup and Configuration

### 1. Environment Variables

Add these to your `.env` file:

```bash
# Apify Configuration
APIFY_API_TOKEN=apify_api_your_token_here
APIFY_ACTOR_ID=apify/linkedin-profile-scraper

# Optional: Custom actor settings
MAX_CONCURRENT_SCRAPING_JOBS=5
SCRAPING_TIMEOUT_MINUTES=30
```

### 2. Get Apify Credentials

1. **Sign up for Apify**: Visit [apify.com](https://apify.com) and create an account
2. **Get API Token**: Go to Settings → Integrations → API tokens
3. **Choose Actor**: Browse the [Apify Store](https://apify.com/store) for LinkedIn scrapers

### 3. Popular LinkedIn Actors

| Actor | Description | Use Case |
|-------|-------------|----------|
| `apify/linkedin-profile-scraper` | Extract profile data | Lead generation |
| `apify/linkedin-company-scraper` | Company information | Company research |
| `apify/linkedin-post-scraper` | Posts and engagement | Content analysis |

## 🚀 Basic Usage

### Direct Apify Client Usage

```python
from services.apify_client import ApifyClient, LinkedInSearchInput

# Initialize client
client = ApifyClient()

# Define search parameters
search_input = LinkedInSearchInput(
    keywords=["software engineer", "python developer"],
    locations=["San Francisco", "New York"],
    industries=["Technology"],
    max_results=100,
    include_private_profiles=False,
    use_residential_proxies=True
)

# Start scraping
run_id = await client.start_linkedin_scraping(search_input)

# Monitor progress
status = await client.get_run_status(run_id)
print(f"Status: {status.status.value}")

# Get results when completed
if status.status == ApifyRunStatus.SUCCEEDED:
    profiles = await client.get_run_results(run_id)
    print(f"Found {len(profiles)} profiles")
```

### High-Level Scraping Manager

```python
from services.linkedin_scraper import scraping_manager

# Start scraping for a campaign
run_id = await scraping_manager.start_campaign_scraping(
    campaign_id=campaign_uuid,
    user_id=user_uuid
)

# Check status
status = await scraping_manager.get_job_status(campaign_uuid)

# Stop if needed
success = await scraping_manager.stop_job(campaign_uuid)
```

## 📊 API Endpoints

### Start Scraping

```http
POST /api/v1/scraping/start
Content-Type: application/json
Authorization: Bearer <token>

{
    "campaign_id": "uuid",
    "force_restart": false
}
```

**Response:**
```json
{
    "campaign_id": "uuid",
    "status": "RUNNING",
    "apify_run_id": "run_123",
    "leads_found": 0,
    "leads_processed": 0,
    "started_at": "2024-01-01T12:00:00Z"
}
```

### Check Status

```http
GET /api/v1/scraping/status/{campaign_id}
Authorization: Bearer <token>
```

**Response:**
```json
{
    "campaign_id": "uuid",
    "status": "RUNNING",
    "apify_run_id": "run_123",
    "progress": {
        "items_found": 45,
        "target_items": 100,
        "percentage": 45.0
    },
    "leads_found": 45,
    "leads_processed": 30,
    "started_at": "2024-01-01T12:00:00Z",
    "estimated_completion": "2024-01-01T12:30:00Z"
}
```

### Stop Scraping

```http
POST /api/v1/scraping/stop/{campaign_id}
Authorization: Bearer <token>
```

## 🔒 Security Best Practices

### 1. API Token Security

```python
# ✅ Good: Use environment variables
APIFY_API_TOKEN = os.getenv("APIFY_API_TOKEN")

# ❌ Bad: Hardcode tokens
APIFY_API_TOKEN = "apify_api_123456789"
```

### 2. Rate Limiting

```python
class ApifyClient:
    def __init__(self):
        self.max_concurrent_requests = 5
        self.request_delay = 1.0  # seconds
        
    async def _make_request(self, url, **kwargs):
        # Implement rate limiting
        await asyncio.sleep(self.request_delay)
        # Make request...
```

### 3. Error Handling

```python
try:
    run_id = await client.start_linkedin_scraping(search_input)
except aiohttp.ClientError as e:
    logger.error(f"Network error: {e}")
    raise HTTPException(status_code=503, detail="Service unavailable")
except Exception as e:
    logger.error(f"Unexpected error: {e}")
    raise HTTPException(status_code=500, detail="Internal server error")
```

## 📈 Monitoring and Logging

### 1. Structured Logging

```python
import structlog

logger = structlog.get_logger()

# Log scraping events
logger.info(
    "scraping_started",
    campaign_id=campaign_id,
    run_id=run_id,
    keywords=search_input.keywords,
    max_results=search_input.max_results
)
```

### 2. Metrics Collection

```python
# Track scraping metrics
metrics = {
    "scraping_jobs_started": 0,
    "scraping_jobs_completed": 0,
    "scraping_jobs_failed": 0,
    "total_leads_scraped": 0,
    "average_scraping_time": 0
}
```

### 3. Health Checks

```python
@router.get("/health/scraping")
async def scraping_health():
    try:
        # Test Apify connection
        client = ApifyClient()
        # Quick connection test
        return {"status": "healthy", "apify_connection": "ok"}
    except Exception as e:
        return {"status": "unhealthy", "error": str(e)}
```

## 🎯 Advanced Features

### 1. Custom Actor Configuration

```python
# Custom actor input for specific use cases
actor_input = {
    "searchQuery": "software engineer",
    "maxResults": 500,
    "proxyConfiguration": {
        "useApifyProxy": True,
        "apifyProxyGroups": ["RESIDENTIAL"],
        "apifyProxyCountry": "US"
    },
    "customSettings": {
        "extractSkills": True,
        "extractExperience": True,
        "extractEducation": True,
        "includeContactInfo": True
    }
}
```

### 2. Batch Processing

```python
async def batch_scrape_campaigns(campaign_ids: List[uuid.UUID]):
    """Process multiple campaigns in batches"""
    batch_size = 3
    
    for i in range(0, len(campaign_ids), batch_size):
        batch = campaign_ids[i:i + batch_size]
        
        # Start batch
        tasks = [
            scraping_manager.start_campaign_scraping(cid, user_id)
            for cid in batch
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Wait before next batch
        await asyncio.sleep(60)
```

### 3. Result Processing Pipeline

```python
async def process_scraping_results(profiles: List[LinkedInProfile]):
    """Process scraped profiles through multiple stages"""
    
    # Stage 1: Data validation
    validated_profiles = [
        profile for profile in profiles
        if profile.full_name and profile.linkedin_url
    ]
    
    # Stage 2: Duplicate detection
    unique_profiles = remove_duplicates(validated_profiles)
    
    # Stage 3: Data enrichment
    enriched_profiles = await enrich_profile_data(unique_profiles)
    
    # Stage 4: AI scoring
    scored_profiles = await score_profiles_with_ai(enriched_profiles)
    
    return scored_profiles
```

## 🚨 Error Handling

### Common Error Scenarios

| Error | Cause | Solution |
|-------|-------|----------|
| `401 Unauthorized` | Invalid API token | Check APIFY_API_TOKEN |
| `402 Payment Required` | Insufficient credits | Add credits to Apify account |
| `429 Too Many Requests` | Rate limit exceeded | Implement backoff strategy |
| `500 Internal Server Error` | Actor failure | Check actor logs in Apify console |

### Retry Strategy

```python
import asyncio
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
async def start_scraping_with_retry(search_input):
    return await client.start_linkedin_scraping(search_input)
```

## 💰 Cost Optimization

### 1. Credit Management

```python
def estimate_scraping_cost(max_results: int) -> int:
    """Estimate Apify credits needed"""
    # Typical cost: 1 credit per 10 profiles
    return max(1, max_results // 10)

def check_user_credits(user: User, estimated_cost: int) -> bool:
    """Check if user has sufficient credits"""
    return user.credits_remaining >= estimated_cost
```

### 2. Smart Filtering

```python
# Use precise search to reduce unnecessary results
search_input = LinkedInSearchInput(
    keywords=["senior software engineer", "python"],  # More specific
    locations=["San Francisco Bay Area"],  # Precise location
    company_sizes=["51-200", "201-500"],  # Target company sizes
    max_results=100  # Reasonable limit
)
```

## 🧪 Testing

### 1. Unit Tests

```python
import pytest
from unittest.mock import AsyncMock, patch

@pytest.mark.asyncio
async def test_start_linkedin_scraping():
    with patch('aiohttp.ClientSession.post') as mock_post:
        mock_post.return_value.__aenter__.return_value.status = 201
        mock_post.return_value.__aenter__.return_value.json = AsyncMock(
            return_value={"data": {"id": "test_run_123"}}
        )
        
        client = ApifyClient()
        search_input = LinkedInSearchInput(keywords=["test"])
        
        run_id = await client.start_linkedin_scraping(search_input)
        assert run_id == "test_run_123"
```

### 2. Integration Tests

```python
@pytest.mark.integration
async def test_full_scraping_workflow():
    """Test complete scraping workflow with real Apify API"""
    client = ApifyClient()
    
    # Start with minimal search
    search_input = LinkedInSearchInput(
        keywords=["test"],
        max_results=1
    )
    
    run_id = await client.start_linkedin_scraping(search_input)
    assert run_id is not None
    
    # Check status
    status = await client.get_run_status(run_id)
    assert status.run_id == run_id
    
    # Abort to avoid charges
    success = await client.abort_run(run_id)
    assert success is True
```

## 📚 Additional Resources

- [Apify API Documentation](https://docs.apify.com/api/v2)
- [LinkedIn Scraper Actor](https://apify.com/apify/linkedin-profile-scraper)
- [Apify Python Client](https://github.com/apify/apify-client-python)
- [Rate Limiting Best Practices](https://docs.apify.com/platform/limits)

## 🆘 Troubleshooting

### Debug Mode

```python
# Enable debug logging
import logging
logging.getLogger("services.apify_client").setLevel(logging.DEBUG)

# Test connection
python examples/apify_integration_example.py
```

### Common Issues

1. **Actor not starting**: Check actor ID and availability
2. **No results**: Verify search parameters and LinkedIn data availability
3. **Timeout errors**: Increase timeout settings or check network connectivity
4. **Rate limiting**: Implement proper delays between requests
