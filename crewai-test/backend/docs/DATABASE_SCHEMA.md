# Database Schema Design for LinkedIn Lead Generation

This document outlines the database schema design for storing LinkedIn lead data with support for both PostgreSQL (SQL) and MongoDB (NoSQL) implementations.

## 🏗️ Schema Overview

The database schema is designed to handle:
- **User Management**: Authentication, subscriptions, and credits
- **Campaign Management**: Lead generation campaigns with search parameters
- **Lead Storage**: LinkedIn profile data with AI scoring
- **Interaction Tracking**: Communication history and engagement
- **Data Export**: Export jobs and file management
- **Analytics**: Performance metrics and reporting

## 📊 Entity Relationship Diagram

```
Users (1) ──── (N) Campaigns (1) ──── (N) Leads
  │                                      │
  │                                      │
  └── (N) Exports                        └── (N) Lead_Interactions
                                         │
                                         └── (N) Lead_Scoring_History
```

## 🐘 PostgreSQL Schema

### Core Tables

#### 1. Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON>HA<PERSON>(100),
    company VARCHAR(255),
    
    -- Subscription management
    subscription_tier VARCHAR(50) DEFAULT 'free',
    credits_remaining INTEGER DEFAULT 100,
    credits_used INTEGER DEFAULT 0,
    
    -- Account status
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 2. Campaigns Table
```sql
CREATE TABLE campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Search parameters
    keywords TEXT[] NOT NULL,
    target_locations TEXT[] DEFAULT '{}',
    target_industries TEXT[] DEFAULT '{}',
    target_company_sizes TEXT[] DEFAULT '{}',
    
    -- Campaign settings
    max_leads INTEGER DEFAULT 1000,
    include_private_profiles BOOLEAN DEFAULT FALSE,
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'draft',
    leads_found INTEGER DEFAULT 0,
    leads_processed INTEGER DEFAULT 0,
    
    -- Apify integration
    apify_run_id VARCHAR(255),
    apify_status VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 3. Leads Table (Main Entity)
```sql
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id),
    
    -- LinkedIn profile data
    linkedin_url VARCHAR(500) UNIQUE,
    full_name VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    headline VARCHAR(500),
    summary TEXT,
    
    -- Location information
    location VARCHAR(255),
    country VARCHAR(100),
    city VARCHAR(100),
    region VARCHAR(100),
    
    -- Professional details
    current_position VARCHAR(255),
    company VARCHAR(255),
    company_size VARCHAR(100),
    industry VARCHAR(255),
    experience_years INTEGER,
    
    -- Flexible data (JSONB)
    skills TEXT[] DEFAULT '{}',
    education JSONB DEFAULT '[]',
    experience JSONB DEFAULT '[]',
    
    -- Contact information
    email VARCHAR(255),
    phone VARCHAR(20),
    website VARCHAR(500),
    
    -- Profile metrics
    connections_count INTEGER,
    followers_count INTEGER,
    profile_image_url VARCHAR(500),
    
    -- AI scoring
    ai_score DECIMAL(3,2) CHECK (ai_score >= 0 AND ai_score <= 1),
    ai_reasoning TEXT,
    ai_criteria_breakdown JSONB DEFAULT '{}',
    ai_model_version VARCHAR(50),
    
    -- Lead management
    status VARCHAR(50) DEFAULT 'new',
    priority VARCHAR(20) DEFAULT 'medium',
    tags TEXT[] DEFAULT '{}',
    notes TEXT,
    
    -- Data quality
    data_quality_score DECIMAL(3,2),
    is_verified BOOLEAN DEFAULT FALSE,
    
    -- Timestamps
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Performance Indexes

```sql
-- Critical indexes for query performance
CREATE INDEX idx_leads_campaign_id ON leads(campaign_id);
CREATE INDEX idx_leads_linkedin_url ON leads(linkedin_url);
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_ai_score ON leads(ai_score DESC);
CREATE INDEX idx_leads_company ON leads(company);

-- Composite indexes for common queries
CREATE INDEX idx_leads_campaign_status ON leads(campaign_id, status);
CREATE INDEX idx_leads_campaign_score ON leads(campaign_id, ai_score DESC);

-- Text search indexes
CREATE INDEX idx_leads_full_name_trgm ON leads USING GIN(full_name gin_trgm_ops);
CREATE INDEX idx_leads_company_trgm ON leads USING GIN(company gin_trgm_ops);

-- JSONB indexes for flexible queries
CREATE INDEX idx_leads_skills ON leads USING GIN(skills);
CREATE INDEX idx_leads_education ON leads USING GIN(education);
```

## 🍃 MongoDB Schema

### Document Structure

#### 1. Users Collection
```javascript
{
  _id: ObjectId,
  email: "<EMAIL>",
  password_hash: "bcrypt_hash",
  first_name: "John",
  last_name: "Doe",
  company: "TechCorp",
  
  // Subscription management
  subscription_tier: "premium", // free, premium, enterprise
  credits_remaining: 1000,
  credits_used: 50,
  
  // Account status
  is_active: true,
  is_verified: true,
  email_notifications: true,
  timezone: "UTC",
  
  // Timestamps
  created_at: ISODate(),
  updated_at: ISODate()
}
```

#### 2. Campaigns Collection
```javascript
{
  _id: ObjectId,
  user_id: ObjectId,
  name: "Software Engineers in SF",
  description: "Target senior software engineers...",
  
  // Search parameters (arrays for flexibility)
  keywords: ["software engineer", "python developer"],
  target_locations: ["San Francisco", "New York"],
  target_industries: ["Technology", "Software"],
  target_company_sizes: ["51-200", "201-500"],
  
  // Campaign settings
  max_leads: 1000,
  include_private_profiles: false,
  
  // Status tracking
  status: "active", // draft, active, paused, completed, failed
  leads_found: 150,
  leads_processed: 120,
  leads_qualified: 85,
  
  // Apify integration
  apify_run_id: "run_abc123",
  apify_status: "SUCCEEDED",
  
  // Timestamps
  created_at: ISODate(),
  updated_at: ISODate(),
  started_at: ISODate(),
  completed_at: ISODate()
}
```

#### 3. Leads Collection (Main Document)
```javascript
{
  _id: ObjectId,
  campaign_id: ObjectId,
  
  // LinkedIn profile information
  linkedin_url: "https://linkedin.com/in/johndoe",
  linkedin_id: "johndoe123",
  full_name: "John Doe",
  first_name: "John",
  last_name: "Doe",
  headline: "Senior Software Engineer at TechCorp",
  summary: "Experienced software engineer with 8+ years...",
  
  // Location (structured for better querying)
  location: "San Francisco, CA, United States",
  country: "United States",
  city: "San Francisco",
  region: "CA",
  
  // Professional details
  current_position: "Senior Software Engineer",
  company: "TechCorp",
  company_url: "https://techcorp.com",
  company_size: "201-500",
  company_industry: "Technology",
  industry: "Software Development",
  experience_years: 8,
  seniority_level: "Senior",
  
  // Skills and education (flexible arrays/objects)
  skills: ["Python", "JavaScript", "React", "Node.js"],
  languages: ["English", "Spanish"],
  education: [
    {
      school: "Stanford University",
      degree: "Bachelor of Science",
      field: "Computer Science",
      start_date: "2014",
      end_date: "2018"
    }
  ],
  experience: [
    {
      title: "Senior Software Engineer",
      company: "TechCorp",
      start_date: "2020-01",
      end_date: "Present",
      description: "Lead development of microservices..."
    }
  ],
  
  // Contact information
  email: "<EMAIL>",
  phone: "******-0123",
  website: "https://johndoe.dev",
  twitter_handle: "@johndoe",
  other_social: {
    github: "johndoe",
    stackoverflow: "johndoe"
  },
  
  // Profile metrics
  connections_count: 500,
  followers_count: 1200,
  profile_image_url: "https://media.licdn.com/...",
  profile_completeness_score: 0.95,
  
  // AI scoring and analysis
  ai_score: 0.87,
  ai_reasoning: "High-quality profile with relevant experience...",
  ai_criteria_breakdown: {
    experience_relevance: 0.9,
    company_quality: 0.8,
    profile_completeness: 0.95,
    skill_match: 0.85
  },
  ai_model_version: "crewai-v1.0",
  ai_confidence_level: 0.92,
  
  // Lead management
  status: "qualified", // new, qualified, contacted, responded, converted, rejected
  priority: "high", // low, medium, high, urgent
  source: "linkedin_scraping",
  tags: ["hot_lead", "senior_engineer", "python"],
  notes: "Excellent candidate for senior role",
  
  // Engagement tracking
  last_contacted: ISODate(),
  contact_attempts: 2,
  response_received: true,
  conversion_date: null,
  
  // Data quality and validation
  data_quality_score: 0.92,
  is_verified: false,
  verification_date: null,
  
  // Timestamps
  scraped_at: ISODate(),
  processed_at: ISODate(),
  created_at: ISODate(),
  updated_at: ISODate()
}
```

### MongoDB Indexes

```javascript
// Essential indexes for performance
db.leads.createIndex({ "campaign_id": 1 });
db.leads.createIndex({ "linkedin_url": 1 }, { unique: true, sparse: true });
db.leads.createIndex({ "email": 1 }, { sparse: true });
db.leads.createIndex({ "status": 1 });
db.leads.createIndex({ "ai_score": -1 });
db.leads.createIndex({ "company": 1 });

// Compound indexes for common queries
db.leads.createIndex({ "campaign_id": 1, "status": 1 });
db.leads.createIndex({ "campaign_id": 1, "ai_score": -1 });
db.leads.createIndex({ "status": 1, "ai_score": -1 });

// Text search index
db.leads.createIndex({
  "full_name": "text",
  "headline": "text",
  "company": "text",
  "summary": "text"
});

// Array field indexes
db.leads.createIndex({ "skills": 1 });
db.leads.createIndex({ "tags": 1 });
```

## 🔄 Data Processing Pipeline

### 1. Apify Result Processing
```python
# Transform Apify LinkedIn profile to database format
async def process_apify_results(campaign_id, apify_profiles):
    stats = {
        'total_profiles': len(apify_profiles),
        'leads_created': 0,
        'leads_updated': 0,
        'duplicates_found': 0,
        'validation_failures': 0
    }
    
    for profile in apify_profiles:
        # Validate profile data
        if not validate_profile_data(profile):
            stats['validation_failures'] += 1
            continue
        
        # Check for duplicates
        existing_lead = find_existing_lead(profile)
        if existing_lead:
            stats['duplicates_found'] += 1
            continue
        
        # Transform and save
        lead_data = transform_profile_to_lead(campaign_id, profile)
        save_lead(lead_data)
        stats['leads_created'] += 1
    
    return stats
```

### 2. Data Validation
```python
def validate_profile_data(profile):
    # Minimum requirements
    if not profile.url:
        return False
    
    if not profile.full_name and not profile.headline:
        return False
    
    # Check for suspicious patterns
    suspicious_patterns = [
        r'^LinkedIn Member$',
        r'^Private Profile$',
        r'^[A-Z]{2,}\s[A-Z]{2,}$'  # All caps names
    ]
    
    for pattern in suspicious_patterns:
        if re.match(pattern, profile.full_name, re.IGNORECASE):
            return False
    
    return True
```

### 3. Duplicate Detection
```python
def find_potential_duplicate(profile, existing_leads):
    for lead in existing_leads:
        similarity_score = calculate_similarity(profile, lead)
        if similarity_score >= 0.85:  # 85% similarity threshold
            return lead
    return None

def calculate_similarity(profile, lead):
    # Compare name, company, location, headline
    # Return similarity score (0.0 to 1.0)
    pass
```

## 📈 Query Examples

### PostgreSQL Queries

```sql
-- Get qualified leads for a campaign
SELECT l.*, c.name as campaign_name
FROM leads l
JOIN campaigns c ON l.campaign_id = c.id
WHERE l.campaign_id = $1 
  AND l.ai_score >= 0.7
  AND l.status = 'new'
ORDER BY l.ai_score DESC;

-- Campaign performance analytics
SELECT 
    c.name,
    COUNT(l.id) as total_leads,
    COUNT(CASE WHEN l.ai_score >= 0.7 THEN 1 END) as qualified_leads,
    AVG(l.ai_score) as avg_score,
    COUNT(CASE WHEN l.status = 'converted' THEN 1 END) as conversions
FROM campaigns c
LEFT JOIN leads l ON c.id = l.campaign_id
WHERE c.user_id = $1
GROUP BY c.id, c.name;

-- Full-text search across leads
SELECT l.*, ts_rank(to_tsvector('english', l.full_name || ' ' || l.headline), query) as rank
FROM leads l, plainto_tsquery('english', $1) query
WHERE to_tsvector('english', l.full_name || ' ' || l.headline) @@ query
ORDER BY rank DESC;
```

### MongoDB Queries

```javascript
// Get qualified leads for a campaign
db.leads.find({
  campaign_id: ObjectId("..."),
  ai_score: { $gte: 0.7 },
  status: "new"
}).sort({ ai_score: -1 });

// Campaign performance analytics
db.leads.aggregate([
  { $match: { campaign_id: ObjectId("...") } },
  { $group: {
    _id: "$campaign_id",
    total_leads: { $sum: 1 },
    qualified_leads: {
      $sum: { $cond: [{ $gte: ["$ai_score", 0.7] }, 1, 0] }
    },
    avg_score: { $avg: "$ai_score" },
    conversions: {
      $sum: { $cond: [{ $eq: ["$status", "converted"] }, 1, 0] }
    }
  }}
]);

// Text search across leads
db.leads.find({
  $text: { $search: "software engineer python" }
}, {
  score: { $meta: "textScore" }
}).sort({ score: { $meta: "textScore" } });

// Complex filtering with arrays
db.leads.find({
  skills: { $in: ["Python", "JavaScript"] },
  "experience.company": "Google",
  ai_score: { $gte: 0.8 }
});
```

## 🚀 Performance Optimization

### PostgreSQL Optimizations
1. **Partitioning**: Partition leads table by campaign_id or date
2. **Materialized Views**: Pre-compute analytics for dashboards
3. **Connection Pooling**: Use pgbouncer for connection management
4. **Query Optimization**: Use EXPLAIN ANALYZE for query tuning

### MongoDB Optimizations
1. **Sharding**: Shard by campaign_id for horizontal scaling
2. **Read Preferences**: Use secondary reads for analytics
3. **Aggregation Pipeline**: Optimize with $match early in pipeline
4. **Index Optimization**: Monitor index usage with explain()

## 🔒 Security Considerations

1. **Data Encryption**: Encrypt PII fields at rest
2. **Access Control**: Row-level security (PostgreSQL) or field-level (MongoDB)
3. **Audit Logging**: Track all data access and modifications
4. **Data Retention**: Implement data purging policies
5. **Backup Strategy**: Regular backups with point-in-time recovery

## 📊 Monitoring & Maintenance

1. **Performance Metrics**: Query response times, index usage
2. **Data Quality**: Monitor duplicate rates, validation failures
3. **Storage Growth**: Track database size and growth patterns
4. **Index Maintenance**: Regular index rebuilding and optimization
5. **Health Checks**: Automated database health monitoring
