# Campaign Management API Endpoints
# CRUD operations for LinkedIn lead generation campaigns

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc
from typing import List, Optional
from datetime import datetime
import logging
import uuid

from core.database import get_db
from api.v1.auth import get_current_user
from models.database import User, Campaign, Lead
from models.schemas import (
    CampaignCreate,
    CampaignUpdate,
    CampaignResponse,
    CampaignList,
    CampaignStatus,
    SuccessResponse,
    ErrorResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/", response_model=CampaignResponse, status_code=status.HTTP_201_CREATED)
async def create_campaign(
    campaign_data: CampaignCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new LinkedIn lead generation campaign
    
    Args:
        campaign_data: Campaign creation data
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CampaignResponse: Created campaign data
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        # Check user's credit limit
        if current_user.credits_remaining < 10:  # Minimum credits required
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Insufficient credits to create campaign"
            )
        
        # Check campaign limit based on subscription tier
        existing_campaigns = db.query(Campaign).filter(
            Campaign.user_id == current_user.id,
            Campaign.status.in_(['active', 'paused'])
        ).count()
        
        max_campaigns = {
            'free': 1,
            'premium': 5,
            'enterprise': 50
        }.get(current_user.subscription_tier, 1)
        
        if existing_campaigns >= max_campaigns:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Maximum {max_campaigns} active campaigns allowed for {current_user.subscription_tier} tier"
            )
        
        # Create new campaign
        new_campaign = Campaign(
            user_id=current_user.id,
            name=campaign_data.name,
            description=campaign_data.description,
            keywords=campaign_data.keywords,
            target_locations=campaign_data.target_locations,
            target_industries=campaign_data.target_industries,
            target_company_sizes=campaign_data.target_company_sizes,
            target_seniority_levels=campaign_data.target_seniority_levels,
            max_leads=campaign_data.max_leads,
            include_private_profiles=campaign_data.include_private_profiles,
            status=CampaignStatus.DRAFT
        )
        
        db.add(new_campaign)
        db.commit()
        db.refresh(new_campaign)
        
        logger.info(f"Campaign created: {new_campaign.id} by user {current_user.email}")
        
        # TODO: Add background task to validate campaign settings
        # background_tasks.add_task(validate_campaign_settings, new_campaign.id)
        
        return CampaignResponse.from_orm(new_campaign)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Campaign creation error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Campaign creation failed"
        )

@router.get("/", response_model=CampaignList)
async def list_campaigns(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    status: Optional[CampaignStatus] = Query(None, description="Filter by status"),
    search: Optional[str] = Query(None, description="Search in campaign names"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List user's campaigns with filtering and pagination
    
    Args:
        page: Page number (1-based)
        size: Number of items per page
        status: Filter by campaign status
        search: Search term for campaign names
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CampaignList: Paginated list of campaigns
    """
    try:
        # Build query
        query = db.query(Campaign).filter(Campaign.user_id == current_user.id)
        
        # Apply filters
        if status:
            query = query.filter(Campaign.status == status)
        
        if search:
            query = query.filter(Campaign.name.ilike(f"%{search}%"))
        
        # Apply sorting
        sort_column = getattr(Campaign, sort_by, Campaign.created_at)
        if sort_order == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * size
        campaigns = query.offset(offset).limit(size).all()
        
        return CampaignList(
            campaigns=[CampaignResponse.from_orm(campaign) for campaign in campaigns],
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"Campaign listing error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve campaigns"
        )

@router.get("/{campaign_id}", response_model=CampaignResponse)
async def get_campaign(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific campaign by ID
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CampaignResponse: Campaign data
        
    Raises:
        HTTPException: If campaign not found or access denied
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        return CampaignResponse.from_orm(campaign)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Campaign retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve campaign"
        )

@router.put("/{campaign_id}", response_model=CampaignResponse)
async def update_campaign(
    campaign_id: uuid.UUID,
    campaign_update: CampaignUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a campaign
    
    Args:
        campaign_id: Campaign UUID
        campaign_update: Campaign update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        CampaignResponse: Updated campaign data
        
    Raises:
        HTTPException: If campaign not found or update fails
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Check if campaign can be updated
        if campaign.status in [CampaignStatus.COMPLETED, CampaignStatus.FAILED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot update completed or failed campaigns"
            )
        
        # Update fields
        update_data = campaign_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(campaign, field, value)
        
        campaign.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(campaign)
        
        logger.info(f"Campaign updated: {campaign_id} by user {current_user.email}")
        return CampaignResponse.from_orm(campaign)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Campaign update error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Campaign update failed"
        )

@router.delete("/{campaign_id}", response_model=SuccessResponse)
async def delete_campaign(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a campaign and all associated leads
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Deletion confirmation
        
    Raises:
        HTTPException: If campaign not found or deletion fails
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Check if campaign can be deleted
        if campaign.status == CampaignStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete active campaign. Pause it first."
            )
        
        # Delete campaign (cascades to leads)
        db.delete(campaign)
        db.commit()
        
        logger.info(f"Campaign deleted: {campaign_id} by user {current_user.email}")
        return SuccessResponse(message="Campaign deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Campaign deletion error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Campaign deletion failed"
        )

@router.post("/{campaign_id}/start", response_model=SuccessResponse)
async def start_campaign(
    campaign_id: uuid.UUID,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start a campaign (begins LinkedIn scraping)
    
    Args:
        campaign_id: Campaign UUID
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Start confirmation
        
    Raises:
        HTTPException: If campaign cannot be started
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Check if campaign can be started
        if campaign.status not in [CampaignStatus.DRAFT, CampaignStatus.PAUSED]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot start campaign with status: {campaign.status}"
            )
        
        # Check user credits
        estimated_cost = min(campaign.max_leads, 1000) // 10  # 1 credit per 10 leads
        if current_user.credits_remaining < estimated_cost:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Insufficient credits to start campaign"
            )
        
        # Update campaign status
        campaign.status = CampaignStatus.ACTIVE
        campaign.started_at = datetime.utcnow()
        campaign.updated_at = datetime.utcnow()
        db.commit()
        
        # TODO: Start scraping in background
        # background_tasks.add_task(start_scraping_job, campaign_id)
        
        logger.info(f"Campaign started: {campaign_id} by user {current_user.email}")
        return SuccessResponse(message="Campaign started successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Campaign start error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start campaign"
        )

@router.post("/{campaign_id}/pause", response_model=SuccessResponse)
async def pause_campaign(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Pause an active campaign
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Pause confirmation
        
    Raises:
        HTTPException: If campaign cannot be paused
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        if campaign.status != CampaignStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Only active campaigns can be paused"
            )
        
        # Update campaign status
        campaign.status = CampaignStatus.PAUSED
        campaign.updated_at = datetime.utcnow()
        db.commit()
        
        # TODO: Pause scraping job
        # pause_scraping_job(campaign.apify_run_id)
        
        logger.info(f"Campaign paused: {campaign_id} by user {current_user.email}")
        return SuccessResponse(message="Campaign paused successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Campaign pause error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to pause campaign"
        )

@router.get("/{campaign_id}/stats")
async def get_campaign_stats(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get detailed campaign statistics
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        dict: Campaign statistics
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Get lead statistics
        leads_query = db.query(Lead).filter(Lead.campaign_id == campaign_id)
        
        total_leads = leads_query.count()
        qualified_leads = leads_query.filter(Lead.ai_score >= 0.7).count()
        contacted_leads = leads_query.filter(Lead.status == 'contacted').count()
        converted_leads = leads_query.filter(Lead.status == 'converted').count()
        
        # Calculate averages
        avg_score = db.query(func.avg(Lead.ai_score)).filter(
            Lead.campaign_id == campaign_id,
            Lead.ai_score.isnot(None)
        ).scalar() or 0
        
        return {
            "campaign_id": campaign_id,
            "total_leads": total_leads,
            "qualified_leads": qualified_leads,
            "contacted_leads": contacted_leads,
            "converted_leads": converted_leads,
            "qualification_rate": (qualified_leads / total_leads * 100) if total_leads > 0 else 0,
            "contact_rate": (contacted_leads / total_leads * 100) if total_leads > 0 else 0,
            "conversion_rate": (converted_leads / contacted_leads * 100) if contacted_leads > 0 else 0,
            "average_score": float(avg_score),
            "progress_percentage": campaign.progress_percentage,
            "status": campaign.status,
            "created_at": campaign.created_at,
            "started_at": campaign.started_at,
            "completed_at": campaign.completed_at
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Campaign stats error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve campaign statistics"
        )
