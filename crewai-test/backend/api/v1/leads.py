# Lead Management API Endpoints
# CRUD operations and filtering for LinkedIn leads

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, func, and_, or_
from typing import List, Optional
import logging
import uuid

from core.database import get_db
from api.v1.auth import get_current_user
from models.database import User, Campaign, Lead
from models.schemas import (
    LeadResponse,
    LeadUpdate,
    LeadFilters,
    LeadList,
    BulkLeadAction,
    LeadStatus,
    SuccessResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/", response_model=LeadList)
async def list_leads(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    campaign_id: Optional[uuid.UUID] = Query(None, description="Filter by campaign"),
    status: Optional[List[LeadStatus]] = Query(None, description="Filter by status"),
    min_score: Optional[float] = Query(None, ge=0.0, le=1.0, description="Minimum AI score"),
    max_score: Optional[float] = Query(None, ge=0.0, le=1.0, description="Maximum AI score"),
    companies: Optional[List[str]] = Query(None, description="Filter by companies"),
    industries: Optional[List[str]] = Query(None, description="Filter by industries"),
    locations: Optional[List[str]] = Query(None, description="Filter by locations"),
    search: Optional[str] = Query(None, description="Search in names and headlines"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List leads with advanced filtering and pagination
    
    Args:
        page: Page number (1-based)
        size: Number of items per page
        campaign_id: Filter by specific campaign
        status: Filter by lead status(es)
        min_score: Minimum AI score filter
        max_score: Maximum AI score filter
        companies: Filter by company names
        industries: Filter by industries
        locations: Filter by locations
        search: Search term for names and headlines
        sort_by: Field to sort by
        sort_order: Sort order (asc/desc)
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        LeadList: Paginated list of leads
    """
    try:
        # Build base query - only leads from user's campaigns
        query = db.query(Lead).join(Campaign).filter(
            Campaign.user_id == current_user.id
        )
        
        # Apply filters
        filters = []
        
        if campaign_id:
            filters.append(Lead.campaign_id == campaign_id)
        
        if status:
            filters.append(Lead.status.in_(status))
        
        if min_score is not None:
            filters.append(Lead.ai_score >= min_score)
        
        if max_score is not None:
            filters.append(Lead.ai_score <= max_score)
        
        if companies:
            filters.append(Lead.company.in_(companies))
        
        if industries:
            filters.append(Lead.industry.in_(industries))
        
        if locations:
            filters.append(Lead.location.in_(locations))
        
        if search:
            search_filter = or_(
                Lead.full_name.ilike(f"%{search}%"),
                Lead.headline.ilike(f"%{search}%"),
                Lead.company.ilike(f"%{search}%")
            )
            filters.append(search_filter)
        
        # Apply all filters
        if filters:
            query = query.filter(and_(*filters))
        
        # Apply sorting
        sort_column = getattr(Lead, sort_by, Lead.created_at)
        if sort_order == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * size
        leads = query.offset(offset).limit(size).all()
        
        # Create filters object for response
        applied_filters = LeadFilters(
            campaign_id=campaign_id,
            status=status,
            min_score=min_score,
            max_score=max_score,
            companies=companies,
            industries=industries,
            locations=locations,
            keywords=[search] if search else None
        )
        
        return LeadList(
            leads=[LeadResponse.from_orm(lead) for lead in leads],
            total=total,
            page=page,
            size=size,
            filters=applied_filters
        )
        
    except Exception as e:
        logger.error(f"Lead listing error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve leads"
        )

@router.get("/{lead_id}", response_model=LeadResponse)
async def get_lead(
    lead_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific lead by ID
    
    Args:
        lead_id: Lead UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        LeadResponse: Lead data
        
    Raises:
        HTTPException: If lead not found or access denied
    """
    try:
        lead = db.query(Lead).join(Campaign).filter(
            Lead.id == lead_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not lead:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Lead not found"
            )
        
        return LeadResponse.from_orm(lead)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Lead retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve lead"
        )

@router.put("/{lead_id}", response_model=LeadResponse)
async def update_lead(
    lead_id: uuid.UUID,
    lead_update: LeadUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a lead
    
    Args:
        lead_id: Lead UUID
        lead_update: Lead update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        LeadResponse: Updated lead data
        
    Raises:
        HTTPException: If lead not found or update fails
    """
    try:
        lead = db.query(Lead).join(Campaign).filter(
            Lead.id == lead_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not lead:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Lead not found"
            )
        
        # Update fields
        update_data = lead_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(lead, field, value)
        
        # Update timestamp if status changed
        if 'status' in update_data:
            if update_data['status'] == 'contacted':
                lead.last_contacted = datetime.utcnow()
        
        lead.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(lead)
        
        logger.info(f"Lead updated: {lead_id} by user {current_user.email}")
        return LeadResponse.from_orm(lead)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Lead update error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Lead update failed"
        )

@router.delete("/{lead_id}", response_model=SuccessResponse)
async def delete_lead(
    lead_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a lead
    
    Args:
        lead_id: Lead UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Deletion confirmation
        
    Raises:
        HTTPException: If lead not found or deletion fails
    """
    try:
        lead = db.query(Lead).join(Campaign).filter(
            Lead.id == lead_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not lead:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Lead not found"
            )
        
        db.delete(lead)
        db.commit()
        
        logger.info(f"Lead deleted: {lead_id} by user {current_user.email}")
        return SuccessResponse(message="Lead deleted successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Lead deletion error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Lead deletion failed"
        )

@router.post("/bulk-action", response_model=SuccessResponse)
async def bulk_lead_action(
    bulk_action: BulkLeadAction,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Perform bulk actions on multiple leads
    
    Args:
        bulk_action: Bulk action configuration
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Action completion confirmation
        
    Raises:
        HTTPException: If action fails
    """
    try:
        # Get leads that belong to user's campaigns
        leads = db.query(Lead).join(Campaign).filter(
            Lead.id.in_(bulk_action.lead_ids),
            Campaign.user_id == current_user.id
        ).all()
        
        if not leads:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No leads found"
            )
        
        if len(leads) != len(bulk_action.lead_ids):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Some leads do not belong to your campaigns"
            )
        
        # Perform action based on type
        updated_count = 0
        
        if bulk_action.action == "update_status":
            new_status = bulk_action.data.get("status")
            if not new_status:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Status is required for update_status action"
                )
            
            for lead in leads:
                lead.status = new_status
                if new_status == "contacted":
                    lead.last_contacted = datetime.utcnow()
                lead.updated_at = datetime.utcnow()
                updated_count += 1
        
        elif bulk_action.action == "add_tags":
            tags_to_add = bulk_action.data.get("tags", [])
            for lead in leads:
                current_tags = set(lead.tags or [])
                current_tags.update(tags_to_add)
                lead.tags = list(current_tags)
                lead.updated_at = datetime.utcnow()
                updated_count += 1
        
        elif bulk_action.action == "remove_tags":
            tags_to_remove = set(bulk_action.data.get("tags", []))
            for lead in leads:
                current_tags = set(lead.tags or [])
                current_tags -= tags_to_remove
                lead.tags = list(current_tags)
                lead.updated_at = datetime.utcnow()
                updated_count += 1
        
        elif bulk_action.action == "delete":
            for lead in leads:
                db.delete(lead)
                updated_count += 1
        
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid action type"
            )
        
        db.commit()
        
        logger.info(f"Bulk action {bulk_action.action} performed on {updated_count} leads by user {current_user.email}")
        return SuccessResponse(message=f"Action performed on {updated_count} leads")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Bulk action error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Bulk action failed"
        )

@router.get("/analytics/summary")
async def get_leads_analytics(
    campaign_id: Optional[uuid.UUID] = Query(None, description="Filter by campaign"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get lead analytics summary
    
    Args:
        campaign_id: Optional campaign filter
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        dict: Analytics summary
    """
    try:
        # Build base query
        query = db.query(Lead).join(Campaign).filter(
            Campaign.user_id == current_user.id
        )
        
        if campaign_id:
            query = query.filter(Lead.campaign_id == campaign_id)
        
        # Get basic counts
        total_leads = query.count()
        qualified_leads = query.filter(Lead.ai_score >= 0.7).count()
        contacted_leads = query.filter(Lead.status == 'contacted').count()
        converted_leads = query.filter(Lead.status == 'converted').count()
        
        # Get status distribution
        status_counts = db.query(
            Lead.status,
            func.count(Lead.id).label('count')
        ).join(Campaign).filter(
            Campaign.user_id == current_user.id
        )
        
        if campaign_id:
            status_counts = status_counts.filter(Lead.campaign_id == campaign_id)
        
        status_distribution = {
            row.status: row.count 
            for row in status_counts.group_by(Lead.status).all()
        }
        
        # Get score distribution
        score_ranges = [
            ("0.0-0.3", 0.0, 0.3),
            ("0.3-0.5", 0.3, 0.5),
            ("0.5-0.7", 0.5, 0.7),
            ("0.7-0.9", 0.7, 0.9),
            ("0.9-1.0", 0.9, 1.0)
        ]
        
        score_distribution = {}
        for range_name, min_score, max_score in score_ranges:
            count = query.filter(
                Lead.ai_score >= min_score,
                Lead.ai_score < max_score
            ).count()
            score_distribution[range_name] = count
        
        # Get top companies and industries
        top_companies = db.query(
            Lead.company,
            func.count(Lead.id).label('count')
        ).join(Campaign).filter(
            Campaign.user_id == current_user.id,
            Lead.company.isnot(None)
        )
        
        if campaign_id:
            top_companies = top_companies.filter(Lead.campaign_id == campaign_id)
        
        top_companies = [
            {"name": row.company, "count": row.count}
            for row in top_companies.group_by(Lead.company).order_by(desc('count')).limit(10).all()
        ]
        
        top_industries = db.query(
            Lead.industry,
            func.count(Lead.id).label('count')
        ).join(Campaign).filter(
            Campaign.user_id == current_user.id,
            Lead.industry.isnot(None)
        )
        
        if campaign_id:
            top_industries = top_industries.filter(Lead.campaign_id == campaign_id)
        
        top_industries = [
            {"name": row.industry, "count": row.count}
            for row in top_industries.group_by(Lead.industry).order_by(desc('count')).limit(10).all()
        ]
        
        # Calculate rates
        qualification_rate = (qualified_leads / total_leads * 100) if total_leads > 0 else 0
        contact_rate = (contacted_leads / total_leads * 100) if total_leads > 0 else 0
        conversion_rate = (converted_leads / contacted_leads * 100) if contacted_leads > 0 else 0
        
        # Get average score
        avg_score = db.query(func.avg(Lead.ai_score)).join(Campaign).filter(
            Campaign.user_id == current_user.id,
            Lead.ai_score.isnot(None)
        )
        
        if campaign_id:
            avg_score = avg_score.filter(Lead.campaign_id == campaign_id)
        
        avg_score = avg_score.scalar() or 0
        
        return {
            "total_leads": total_leads,
            "qualified_leads": qualified_leads,
            "contacted_leads": contacted_leads,
            "converted_leads": converted_leads,
            "qualification_rate": round(qualification_rate, 2),
            "contact_rate": round(contact_rate, 2),
            "conversion_rate": round(conversion_rate, 2),
            "average_score": round(float(avg_score), 3),
            "status_distribution": status_distribution,
            "score_distribution": score_distribution,
            "top_companies": top_companies,
            "top_industries": top_industries
        }
        
    except Exception as e:
        logger.error(f"Analytics error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analytics"
        )
