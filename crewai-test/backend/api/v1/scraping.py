# LinkedIn Scraping API Endpoints
# Apify integration for LinkedIn data scraping with async job management

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
import asyncio
import logging
import uuid
from datetime import datetime, timedelta

from core.database import get_db
from core.config import settings
from api.v1.auth import get_current_user
from models.database import User, Campaign, Lead
from models.schemas import (
    ScrapingRequest,
    ScrapingStatus,
    SuccessResponse,
    ErrorResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# Import Apify service (this would be implemented separately)
try:
    from services.apify_service import ApifyService, LinkedInSearchParams
    apify_service = ApifyService({
        "api_token": settings.APIFY_API_TOKEN,
        "actor_id": settings.APIFY_ACTOR_ID
    })
except ImportError:
    logger.warning("Apify service not available - using mock implementation")
    apify_service = None

class MockApifyService:
    """Mock Apify service for development/testing"""
    
    async def start_linkedin_scraping(self, search_params) -> str:
        """Mock scraping start - returns fake run ID"""
        return f"mock_run_{uuid.uuid4().hex[:8]}"
    
    async def get_run_status(self, run_id: str) -> Dict[str, Any]:
        """Mock status check"""
        return {
            "id": run_id,
            "status": "RUNNING",
            "startedAt": datetime.utcnow().isoformat(),
            "stats": {
                "itemCount": 50
            }
        }
    
    async def get_run_results(self, run_id: str) -> list:
        """Mock results - returns sample LinkedIn data"""
        return [
            {
                "url": f"https://linkedin.com/in/user{i}",
                "fullName": f"Test User {i}",
                "headline": f"Software Engineer at Company {i}",
                "location": "San Francisco, CA",
                "company": f"Tech Company {i}",
                "companySize": "51-200",
                "industry": "Technology",
                "skills": ["Python", "JavaScript", "React"],
                "connectionsCount": 500 + i * 10
            }
            for i in range(1, 11)  # Return 10 mock leads
        ]

# Use mock service if real one is not available
if not apify_service:
    apify_service = MockApifyService()

@router.post("/start", response_model=ScrapingStatus)
async def start_scraping(
    scraping_request: ScrapingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start LinkedIn scraping for a campaign using Apify actors
    
    Args:
        scraping_request: Scraping configuration
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        ScrapingStatus: Scraping job status
        
    Raises:
        HTTPException: If scraping cannot be started
    """
    try:
        # Get campaign
        campaign = db.query(Campaign).filter(
            Campaign.id == scraping_request.campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Check if campaign is in correct status
        if campaign.status not in ["active", "paused"] and not scraping_request.force_restart:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign must be active to start scraping"
            )
        
        # Check if scraping is already running
        if campaign.apify_run_id and campaign.apify_status == "RUNNING" and not scraping_request.force_restart:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Scraping is already running for this campaign"
            )
        
        # Check user credits
        estimated_cost = min(campaign.max_leads, 1000) // 10  # 1 credit per 10 leads
        if current_user.credits_remaining < estimated_cost:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Insufficient credits to start scraping"
            )
        
        # Prepare search parameters for Apify
        search_params = LinkedInSearchParams(
            keywords=campaign.keywords,
            locations=campaign.target_locations,
            industries=campaign.target_industries,
            company_sizes=campaign.target_company_sizes,
            seniority_levels=campaign.target_seniority_levels,
            max_results=campaign.max_leads,
            include_private_profiles=campaign.include_private_profiles
        )
        
        # Start Apify scraping job
        run_id = await apify_service.start_linkedin_scraping(search_params)
        
        # Update campaign with run ID
        campaign.apify_run_id = run_id
        campaign.apify_status = "RUNNING"
        campaign.status = "active"
        campaign.started_at = datetime.utcnow()
        campaign.updated_at = datetime.utcnow()
        db.commit()
        
        # Start background monitoring task
        background_tasks.add_task(
            monitor_scraping_job,
            campaign.id,
            run_id,
            current_user.id
        )
        
        logger.info(f"Scraping started for campaign {campaign.id}, run_id: {run_id}")
        
        return ScrapingStatus(
            campaign_id=campaign.id,
            status="RUNNING",
            apify_run_id=run_id,
            leads_found=0,
            leads_processed=0,
            started_at=campaign.started_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Scraping start error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start scraping"
        )

@router.get("/status/{campaign_id}", response_model=ScrapingStatus)
async def get_scraping_status(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current scraping status for a campaign
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        ScrapingStatus: Current scraping status
        
    Raises:
        HTTPException: If campaign not found
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Get latest status from Apify if run is active
        apify_status = None
        progress = None
        estimated_completion = None
        error_message = None
        
        if campaign.apify_run_id and campaign.apify_status == "RUNNING":
            try:
                apify_data = await apify_service.get_run_status(campaign.apify_run_id)
                apify_status = apify_data.get("status", "UNKNOWN")
                
                # Calculate estimated completion time
                if apify_status == "RUNNING":
                    stats = apify_data.get("stats", {})
                    items_found = stats.get("itemCount", 0)
                    
                    if items_found > 0 and campaign.started_at:
                        elapsed = datetime.utcnow() - campaign.started_at
                        rate = items_found / elapsed.total_seconds() if elapsed.total_seconds() > 0 else 0
                        
                        if rate > 0:
                            remaining_items = campaign.max_leads - items_found
                            estimated_seconds = remaining_items / rate
                            estimated_completion = datetime.utcnow() + timedelta(seconds=estimated_seconds)
                    
                    progress = {
                        "items_found": items_found,
                        "target_items": campaign.max_leads,
                        "percentage": min(100, (items_found / campaign.max_leads) * 100) if campaign.max_leads > 0 else 0
                    }
                
            except Exception as e:
                logger.warning(f"Failed to get Apify status: {e}")
                error_message = "Unable to fetch scraping status"
        
        return ScrapingStatus(
            campaign_id=campaign.id,
            status=apify_status or campaign.apify_status or "UNKNOWN",
            apify_run_id=campaign.apify_run_id,
            progress=progress,
            leads_found=campaign.leads_found,
            leads_processed=campaign.leads_processed,
            started_at=campaign.started_at,
            estimated_completion=estimated_completion,
            error_message=error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Status check error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get scraping status"
        )

@router.post("/stop/{campaign_id}", response_model=SuccessResponse)
async def stop_scraping(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Stop active scraping for a campaign
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Stop confirmation
        
    Raises:
        HTTPException: If campaign not found or cannot be stopped
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        if not campaign.apify_run_id or campaign.apify_status != "RUNNING":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No active scraping to stop"
            )
        
        # TODO: Implement Apify run abortion
        # await apify_service.abort_run(campaign.apify_run_id)
        
        # Update campaign status
        campaign.apify_status = "ABORTED"
        campaign.status = "paused"
        campaign.updated_at = datetime.utcnow()
        db.commit()
        
        logger.info(f"Scraping stopped for campaign {campaign_id}")
        return SuccessResponse(message="Scraping stopped successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Scraping stop error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop scraping"
        )

async def monitor_scraping_job(campaign_id: uuid.UUID, run_id: str, user_id: uuid.UUID):
    """
    Background task to monitor Apify scraping job and process results
    
    Args:
        campaign_id: Campaign UUID
        run_id: Apify run ID
        user_id: User UUID
    """
    try:
        logger.info(f"Starting monitoring for scraping job {run_id}")
        
        # Poll for completion
        max_wait_time = settings.SCRAPING_TIMEOUT_MINUTES * 60  # Convert to seconds
        poll_interval = 30  # Poll every 30 seconds
        elapsed_time = 0
        
        while elapsed_time < max_wait_time:
            try:
                # Get database session
                db = next(get_db())
                
                # Check run status
                status_data = await apify_service.get_run_status(run_id)
                status = status_data.get("status")
                
                # Update campaign with current stats
                campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
                if campaign:
                    stats = status_data.get("stats", {})
                    campaign.leads_found = stats.get("itemCount", 0)
                    campaign.apify_status = status
                    campaign.updated_at = datetime.utcnow()
                    db.commit()
                
                logger.info(f"Scraping job {run_id} status: {status}")
                
                if status == "SUCCEEDED":
                    # Get results and process them
                    results = await apify_service.get_run_results(run_id)
                    await process_scraping_results(campaign_id, results, user_id)
                    break
                elif status in ["FAILED", "ABORTED", "TIMED-OUT"]:
                    logger.error(f"Scraping job {run_id} failed with status: {status}")
                    # Update campaign status
                    if campaign:
                        campaign.status = "failed"
                        campaign.apify_status = status
                        db.commit()
                    break
                
                db.close()
                
            except Exception as e:
                logger.error(f"Error monitoring scraping job {run_id}: {e}")
            
            await asyncio.sleep(poll_interval)
            elapsed_time += poll_interval
        
        if elapsed_time >= max_wait_time:
            logger.warning(f"Scraping job {run_id} monitoring timed out")
            
    except Exception as e:
        logger.error(f"Fatal error monitoring scraping job {run_id}: {e}")

async def process_scraping_results(campaign_id: uuid.UUID, results: list, user_id: uuid.UUID):
    """
    Process scraped LinkedIn data and save to database
    
    Args:
        campaign_id: Campaign UUID
        results: List of scraped LinkedIn profiles
        user_id: User UUID
    """
    try:
        logger.info(f"Processing {len(results)} scraped results for campaign {campaign_id}")
        
        db = next(get_db())
        
        # Transform and save leads
        leads_created = 0
        for item in results:
            try:
                # Check if lead already exists
                existing_lead = db.query(Lead).filter(
                    Lead.linkedin_url == item.get("url")
                ).first()
                
                if existing_lead:
                    continue  # Skip duplicates
                
                # Create new lead
                lead = Lead(
                    campaign_id=campaign_id,
                    linkedin_url=item.get("url"),
                    full_name=item.get("fullName"),
                    headline=item.get("headline"),
                    location=item.get("location"),
                    company=item.get("company"),
                    company_size=item.get("companySize"),
                    industry=item.get("industry"),
                    skills=item.get("skills", []),
                    connections_count=item.get("connectionsCount"),
                    contact_info={
                        "email": item.get("email", ""),
                        "phone": item.get("phone", "")
                    },
                    scraped_at=datetime.utcnow(),
                    status="new"
                )
                
                db.add(lead)
                leads_created += 1
                
            except Exception as e:
                logger.warning(f"Error processing lead: {e}")
                continue
        
        # Update campaign
        campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
        if campaign:
            campaign.leads_found = leads_created
            campaign.status = "completed"
            campaign.apify_status = "SUCCEEDED"
            campaign.completed_at = datetime.utcnow()
            campaign.updated_at = datetime.utcnow()
        
        # Update user credits
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            credits_used = leads_created // 10  # 1 credit per 10 leads
            user.credits_remaining = max(0, user.credits_remaining - credits_used)
            user.credits_used += credits_used
        
        db.commit()
        db.close()
        
        logger.info(f"Successfully processed {leads_created} leads for campaign {campaign_id}")
        
        # TODO: Trigger AI scoring in background
        # background_tasks.add_task(start_ai_scoring, campaign_id)
        
    except Exception as e:
        logger.error(f"Error processing scraping results: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()
