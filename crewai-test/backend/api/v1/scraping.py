# LinkedIn Scraping API Endpoints
# Apify integration for LinkedIn data scraping with async job management

from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
import asyncio
import logging
import uuid
from datetime import datetime, timedelta

from core.database import get_db
from core.config import settings
from api.v1.auth import get_current_user
from models.database import User, Campaign, Lead
from models.schemas import (
    ScrapingRequest,
    ScrapingStatus,
    SuccessResponse,
    ErrorResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# Import Apify services
from services.apify_client import ApifyClient, LinkedInSearchInput
from services.linkedin_scraper import ScrapingJobManager, scraping_manager

@router.post("/start", response_model=ScrapingStatus)
async def start_scraping(
    scraping_request: ScrapingRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Start LinkedIn scraping for a campaign using Apify actors
    
    Args:
        scraping_request: Scraping configuration
        background_tasks: FastAPI background tasks
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        ScrapingStatus: Scraping job status
        
    Raises:
        HTTPException: If scraping cannot be started
    """
    try:
        # Get campaign
        campaign = db.query(Campaign).filter(
            Campaign.id == scraping_request.campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Check if campaign is in correct status
        if campaign.status not in ["active", "paused"] and not scraping_request.force_restart:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Campaign must be active to start scraping"
            )
        
        # Check if scraping is already running
        if campaign.apify_run_id and campaign.apify_status == "RUNNING" and not scraping_request.force_restart:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Scraping is already running for this campaign"
            )
        
        # Check user credits
        estimated_cost = min(campaign.max_leads, 1000) // 10  # 1 credit per 10 leads
        if current_user.credits_remaining < estimated_cost:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Insufficient credits to start scraping"
            )
        
        # Start scraping using the scraping manager
        run_id = await scraping_manager.start_campaign_scraping(
            campaign_id=campaign.id,
            user_id=current_user.id
        )
        
        # Update campaign with run ID
        campaign.apify_run_id = run_id
        campaign.apify_status = "RUNNING"
        campaign.status = "active"
        campaign.started_at = datetime.utcnow()
        campaign.updated_at = datetime.utcnow()
        db.commit()
        
        # Monitoring is handled by the scraping manager
        
        logger.info(f"Scraping started for campaign {campaign.id}, run_id: {run_id}")
        
        return ScrapingStatus(
            campaign_id=campaign.id,
            status="RUNNING",
            apify_run_id=run_id,
            leads_found=0,
            leads_processed=0,
            started_at=campaign.started_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Scraping start error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start scraping"
        )

@router.get("/status/{campaign_id}", response_model=ScrapingStatus)
async def get_scraping_status(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current scraping status for a campaign
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        ScrapingStatus: Current scraping status
        
    Raises:
        HTTPException: If campaign not found
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Get status from scraping manager
        job_status = await scraping_manager.get_job_status(campaign.id)

        if job_status:
            return ScrapingStatus(
                campaign_id=campaign.id,
                status=job_status.get("status", "UNKNOWN"),
                apify_run_id=job_status.get("run_id"),
                progress=job_status.get("progress"),
                leads_found=job_status.get("leads_found", campaign.leads_found),
                leads_processed=job_status.get("leads_processed", campaign.leads_processed),
                started_at=job_status.get("started_at", campaign.started_at),
                estimated_completion=job_status.get("estimated_completion"),
                error_message=job_status.get("error_message")
            )
        
        # Fallback to campaign data if no active job
        return ScrapingStatus(
            campaign_id=campaign.id,
            status=campaign.apify_status or "UNKNOWN",
            apify_run_id=campaign.apify_run_id,
            progress=None,
            leads_found=campaign.leads_found,
            leads_processed=campaign.leads_processed,
            started_at=campaign.started_at,
            estimated_completion=None,
            error_message=None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Status check error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get scraping status"
        )

@router.post("/stop/{campaign_id}", response_model=SuccessResponse)
async def stop_scraping(
    campaign_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Stop active scraping for a campaign
    
    Args:
        campaign_id: Campaign UUID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        SuccessResponse: Stop confirmation
        
    Raises:
        HTTPException: If campaign not found or cannot be stopped
    """
    try:
        campaign = db.query(Campaign).filter(
            Campaign.id == campaign_id,
            Campaign.user_id == current_user.id
        ).first()
        
        if not campaign:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Campaign not found"
            )
        
        # Use scraping manager to stop the job
        success = await scraping_manager.stop_job(campaign.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No active scraping to stop or failed to stop"
            )

        logger.info(f"Scraping stopped for campaign {campaign_id}")
        return SuccessResponse(message="Scraping stopped successfully")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Scraping stop error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to stop scraping"
        )

# Note: Monitoring and result processing is now handled by the ScrapingJobManager
# in services/linkedin_scraper.py
