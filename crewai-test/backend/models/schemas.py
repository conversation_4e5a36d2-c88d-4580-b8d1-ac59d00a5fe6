# Pydantic Schemas
# Request/Response models for API endpoints

from pydantic import BaseModel, EmailStr, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum
import uuid

# Enums for validation
class SubscriptionTier(str, Enum):
    FREE = "free"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"

class CampaignStatus(str, Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"

class LeadStatus(str, Enum):
    NEW = "new"
    QUALIFIED = "qualified"
    CONTACTED = "contacted"
    CONVERTED = "converted"
    REJECTED = "rejected"

class ExportFormat(str, Enum):
    CSV = "csv"
    XLSX = "xlsx"
    JSON = "json"

class ExportStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

# Base schemas
class BaseSchema(BaseModel):
    """Base schema with common configuration"""
    class Config:
        from_attributes = True
        use_enum_values = True

# Authentication Schemas
class UserRegistration(BaseModel):
    """Schema for user registration"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, description="User password (min 8 characters)")
    first_name: Optional[str] = Field(None, max_length=100, description="First name")
    last_name: Optional[str] = Field(None, max_length=100, description="Last name")
    company: Optional[str] = Field(None, max_length=255, description="Company name")
    
    @validator('password')
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class UserLogin(BaseModel):
    """Schema for user login"""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")

class TokenResponse(BaseModel):
    """Schema for authentication token response"""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")

class RefreshTokenRequest(BaseModel):
    """Schema for token refresh request"""
    refresh_token: str = Field(..., description="Refresh token")

class UserProfile(BaseSchema):
    """Schema for user profile response"""
    id: uuid.UUID
    email: EmailStr
    first_name: Optional[str]
    last_name: Optional[str]
    company: Optional[str]
    subscription_tier: SubscriptionTier
    credits_remaining: int
    credits_used: int
    is_active: bool
    is_verified: bool
    created_at: datetime
    last_login: Optional[datetime]

class UserUpdate(BaseModel):
    """Schema for updating user profile"""
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    company: Optional[str] = Field(None, max_length=255)
    email_notifications: Optional[bool] = None
    timezone: Optional[str] = Field(None, max_length=50)

# Campaign Schemas
class CampaignCreate(BaseModel):
    """Schema for creating a new campaign"""
    name: str = Field(..., min_length=1, max_length=255, description="Campaign name")
    description: Optional[str] = Field(None, description="Campaign description")
    keywords: List[str] = Field(..., min_items=1, description="Search keywords")
    target_locations: Optional[List[str]] = Field(default=[], description="Target locations")
    target_industries: Optional[List[str]] = Field(default=[], description="Target industries")
    target_company_sizes: Optional[List[str]] = Field(default=[], description="Target company sizes")
    target_seniority_levels: Optional[List[str]] = Field(default=[], description="Target seniority levels")
    max_leads: Optional[int] = Field(default=1000, ge=1, le=10000, description="Maximum leads to find")
    include_private_profiles: Optional[bool] = Field(default=False, description="Include private profiles")
    
    @validator('keywords')
    def validate_keywords(cls, v):
        """Validate keywords list"""
        if not v:
            raise ValueError('At least one keyword is required')
        # Remove empty strings and duplicates
        keywords = list(set([k.strip() for k in v if k.strip()]))
        if not keywords:
            raise ValueError('At least one non-empty keyword is required')
        return keywords

class CampaignUpdate(BaseModel):
    """Schema for updating a campaign"""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    status: Optional[CampaignStatus] = None
    max_leads: Optional[int] = Field(None, ge=1, le=10000)

class CampaignResponse(BaseSchema):
    """Schema for campaign response"""
    id: uuid.UUID
    user_id: uuid.UUID
    name: str
    description: Optional[str]
    keywords: List[str]
    target_locations: List[str]
    target_industries: List[str]
    target_company_sizes: List[str]
    target_seniority_levels: List[str]
    max_leads: int
    status: CampaignStatus
    leads_found: int
    leads_processed: int
    leads_qualified: int
    progress_percentage: float
    qualification_rate: float
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]

class CampaignList(BaseModel):
    """Schema for campaign list response"""
    campaigns: List[CampaignResponse]
    total: int
    page: int
    size: int

# Lead Schemas
class LeadResponse(BaseSchema):
    """Schema for lead response"""
    id: uuid.UUID
    campaign_id: uuid.UUID
    linkedin_url: Optional[str]
    full_name: Optional[str]
    headline: Optional[str]
    location: Optional[str]
    company: Optional[str]
    company_size: Optional[str]
    industry: Optional[str]
    experience_years: Optional[int]
    current_position: Optional[str]
    skills: List[str]
    ai_score: Optional[float]
    ai_reasoning: Optional[str]
    score_percentage: Optional[float]
    is_qualified: bool
    status: LeadStatus
    priority: str
    tags: List[str]
    notes: Optional[str]
    scraped_at: datetime
    processed_at: Optional[datetime]
    last_contacted: Optional[datetime]

class LeadUpdate(BaseModel):
    """Schema for updating a lead"""
    status: Optional[LeadStatus] = None
    priority: Optional[str] = Field(None, regex="^(low|medium|high)$")
    tags: Optional[List[str]] = None
    notes: Optional[str] = None

class LeadFilters(BaseModel):
    """Schema for lead filtering"""
    campaign_id: Optional[uuid.UUID] = None
    status: Optional[List[LeadStatus]] = None
    min_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    max_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    companies: Optional[List[str]] = None
    industries: Optional[List[str]] = None
    locations: Optional[List[str]] = None
    keywords: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None

class LeadList(BaseModel):
    """Schema for lead list response"""
    leads: List[LeadResponse]
    total: int
    page: int
    size: int
    filters: Optional[LeadFilters]

class BulkLeadAction(BaseModel):
    """Schema for bulk lead actions"""
    lead_ids: List[uuid.UUID] = Field(..., min_items=1, description="List of lead IDs")
    action: str = Field(..., regex="^(update_status|add_tags|remove_tags|delete)$")
    data: Dict[str, Any] = Field(..., description="Action-specific data")

# Scraping Schemas
class ScrapingRequest(BaseModel):
    """Schema for starting a scraping job"""
    campaign_id: uuid.UUID = Field(..., description="Campaign ID to scrape for")
    force_restart: Optional[bool] = Field(default=False, description="Force restart if already running")

class ScrapingStatus(BaseModel):
    """Schema for scraping status response"""
    campaign_id: uuid.UUID
    status: str  # running, succeeded, failed, aborted
    apify_run_id: Optional[str]
    progress: Optional[Dict[str, Any]]
    leads_found: int
    leads_processed: int
    started_at: Optional[datetime]
    estimated_completion: Optional[datetime]
    error_message: Optional[str]

# Export Schemas
class ExportRequest(BaseModel):
    """Schema for creating an export"""
    campaign_id: Optional[uuid.UUID] = Field(None, description="Campaign ID (optional, exports all if not provided)")
    format: ExportFormat = Field(..., description="Export format")
    filters: Optional[LeadFilters] = Field(None, description="Filters to apply")
    columns: Optional[List[str]] = Field(None, description="Columns to include (all if not provided)")

class ExportResponse(BaseSchema):
    """Schema for export response"""
    id: uuid.UUID
    user_id: uuid.UUID
    campaign_id: Optional[uuid.UUID]
    format: ExportFormat
    status: ExportStatus
    total_records: Optional[int]
    processed_records: int
    progress_percentage: float
    file_path: Optional[str]
    download_url: Optional[str]
    file_size: Optional[int]
    created_at: datetime
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    expires_at: Optional[datetime]
    error_message: Optional[str]

# Analytics Schemas
class CampaignAnalytics(BaseModel):
    """Schema for campaign analytics"""
    total_campaigns: int
    active_campaigns: int
    completed_campaigns: int
    total_leads: int
    qualified_leads: int
    average_score: float
    qualification_rate: float
    top_industries: List[Dict[str, Union[str, int]]]
    top_locations: List[Dict[str, Union[str, int]]]
    score_distribution: Dict[str, int]

class UserAnalytics(BaseModel):
    """Schema for user analytics"""
    credits_used: int
    credits_remaining: int
    campaigns_created: int
    leads_generated: int
    qualified_leads: int
    conversion_rate: float
    activity_timeline: List[Dict[str, Any]]

# Error Schemas
class ErrorResponse(BaseModel):
    """Schema for error responses"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ValidationError(BaseModel):
    """Schema for validation error responses"""
    error: str = "validation_error"
    message: str = "Request validation failed"
    details: List[Dict[str, Any]] = Field(..., description="Validation error details")

# Success Schemas
class SuccessResponse(BaseModel):
    """Schema for success responses"""
    success: bool = True
    message: str
    data: Optional[Dict[str, Any]] = None
