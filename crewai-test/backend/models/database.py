# Database Models
# SQLAlchemy ORM models for the LinkedIn Lead Generation application

from sqlalchemy import Column, String, Integer, DateTime, Text, ARRAY, Boolean, ForeignKey, DECIMAL, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import uuid

from core.database import Base

class TimestampMixin:
    """Mixin to add timestamp fields to models"""
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)

class User(Base, TimestampMixin):
    """
    User model for authentication and user management
    
    Stores user account information, subscription details, and preferences
    """
    __tablename__ = "users"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Authentication fields
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Profile information
    first_name = Column(String(100))
    last_name = Column(String(100))
    company = Column(String(255))
    phone = Column(String(20))
    
    # Subscription and billing
    subscription_tier = Column(String(50), default="free", nullable=False)
    credits_remaining = Column(Integer, default=100, nullable=False)
    credits_used = Column(Integer, default=0, nullable=False)
    
    # Settings and preferences
    timezone = Column(String(50), default="UTC")
    email_notifications = Column(Boolean, default=True)
    
    # Security fields
    last_login = Column(DateTime)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime)
    
    # Relationships
    campaigns = relationship("Campaign", back_populates="user", cascade="all, delete-orphan")
    exports = relationship("Export", back_populates="user", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email})>"
    
    @property
    def full_name(self):
        """Get user's full name"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.email
    
    @property
    def is_premium(self):
        """Check if user has premium subscription"""
        return self.subscription_tier in ["premium", "enterprise"]

class Campaign(Base, TimestampMixin):
    """
    Campaign model for LinkedIn lead generation campaigns
    
    Stores campaign configuration, search parameters, and execution status
    """
    __tablename__ = "campaigns"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to user
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    
    # Campaign basic info
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Search parameters
    keywords = Column(ARRAY(String), nullable=False)
    target_locations = Column(ARRAY(String), default=[])
    target_industries = Column(ARRAY(String), default=[])
    target_company_sizes = Column(ARRAY(String), default=[])
    target_seniority_levels = Column(ARRAY(String), default=[])
    
    # Campaign limits and settings
    max_leads = Column(Integer, default=1000, nullable=False)
    include_private_profiles = Column(Boolean, default=False)
    
    # Execution status
    status = Column(String(50), default="draft", nullable=False)  # draft, active, paused, completed, failed
    
    # Apify integration
    apify_run_id = Column(String(255))
    apify_status = Column(String(50))  # running, succeeded, failed, aborted
    
    # Progress tracking
    leads_found = Column(Integer, default=0)
    leads_processed = Column(Integer, default=0)
    leads_qualified = Column(Integer, default=0)
    
    # Execution timestamps
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    
    # Relationships
    user = relationship("User", back_populates="campaigns")
    leads = relationship("Lead", back_populates="campaign", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Campaign(id={self.id}, name={self.name}, status={self.status})>"
    
    @property
    def progress_percentage(self):
        """Calculate campaign progress percentage"""
        if self.max_leads == 0:
            return 0
        return min(100, (self.leads_found / self.max_leads) * 100)
    
    @property
    def qualification_rate(self):
        """Calculate lead qualification rate"""
        if self.leads_processed == 0:
            return 0
        return (self.leads_qualified / self.leads_processed) * 100

class Lead(Base, TimestampMixin):
    """
    Lead model for storing LinkedIn profile data and AI scoring
    
    Stores scraped LinkedIn profile information and AI-generated quality scores
    """
    __tablename__ = "leads"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign key to campaign
    campaign_id = Column(UUID(as_uuid=True), ForeignKey("campaigns.id"), nullable=False, index=True)
    
    # LinkedIn profile data
    linkedin_url = Column(String(500), unique=True, index=True)
    full_name = Column(String(255), index=True)
    headline = Column(String(500))
    summary = Column(Text)
    location = Column(String(255))
    
    # Company information
    company = Column(String(255), index=True)
    company_size = Column(String(100))
    industry = Column(String(255), index=True)
    
    # Professional details
    experience_years = Column(Integer)
    current_position = Column(String(255))
    education = Column(JSON)  # Store education as JSON array
    skills = Column(ARRAY(String))
    
    # Contact information
    contact_info = Column(JSON)  # Store contact details as JSON
    profile_image_url = Column(String(500))
    connections_count = Column(Integer)
    
    # AI scoring
    ai_score = Column(DECIMAL(3,2))  # Score from 0.00 to 1.00
    ai_reasoning = Column(Text)
    ai_criteria_breakdown = Column(JSON)  # Detailed scoring breakdown
    ai_model_version = Column(String(50))
    
    # Lead management
    status = Column(String(50), default="new", nullable=False, index=True)  # new, qualified, contacted, converted, rejected
    priority = Column(String(20), default="medium")  # low, medium, high
    tags = Column(ARRAY(String), default=[])
    notes = Column(Text)
    
    # Timestamps
    scraped_at = Column(DateTime, default=func.now())
    processed_at = Column(DateTime)
    last_contacted = Column(DateTime)
    
    # Relationships
    campaign = relationship("Campaign", back_populates="leads")
    scoring_history = relationship("ScoringHistory", back_populates="lead", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Lead(id={self.id}, name={self.full_name}, score={self.ai_score})>"
    
    @property
    def score_percentage(self):
        """Get AI score as percentage"""
        if self.ai_score is None:
            return None
        return float(self.ai_score) * 100
    
    @property
    def is_qualified(self):
        """Check if lead is qualified based on score threshold"""
        if self.ai_score is None:
            return False
        return float(self.ai_score) >= 0.7  # 70% threshold

class ScoringHistory(Base, TimestampMixin):
    """
    Scoring history model for tracking AI score changes over time
    
    Maintains a history of all scoring attempts for audit and improvement
    """
    __tablename__ = "scoring_history"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # Foreign key to lead
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False, index=True)
    
    # Scoring details
    score = Column(DECIMAL(3,2), nullable=False)
    reasoning = Column(Text)
    criteria_breakdown = Column(JSON)
    model_version = Column(String(50))
    criteria_weights = Column(JSON)
    
    # Execution details
    processing_time_ms = Column(Integer)  # Time taken to score in milliseconds
    scored_at = Column(DateTime, default=func.now(), nullable=False)
    
    # Relationships
    lead = relationship("Lead", back_populates="scoring_history")
    
    def __repr__(self):
        return f"<ScoringHistory(id={self.id}, lead_id={self.lead_id}, score={self.score})>"

class Export(Base, TimestampMixin):
    """
    Export model for tracking data export jobs
    
    Manages CSV, Excel, and JSON export requests and their status
    """
    __tablename__ = "exports"
    
    # Primary key
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, index=True)
    
    # Foreign keys
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True)
    campaign_id = Column(UUID(as_uuid=True), ForeignKey("campaigns.id"))
    
    # Export configuration
    format = Column(String(20), nullable=False)  # csv, xlsx, json
    filters = Column(JSON)  # Export filters as JSON
    columns = Column(ARRAY(String))  # Selected columns to export
    
    # File details
    file_path = Column(String(500))
    file_size = Column(Integer)  # File size in bytes
    download_url = Column(String(500))
    
    # Status tracking
    status = Column(String(50), default="pending", nullable=False, index=True)  # pending, processing, completed, failed
    total_records = Column(Integer)
    processed_records = Column(Integer, default=0)
    error_message = Column(Text)
    
    # Timestamps
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    expires_at = Column(DateTime)  # When the download link expires
    
    # Relationships
    user = relationship("User", back_populates="exports")
    
    def __repr__(self):
        return f"<Export(id={self.id}, format={self.format}, status={self.status})>"
    
    @property
    def progress_percentage(self):
        """Calculate export progress percentage"""
        if not self.total_records or self.total_records == 0:
            return 0
        return min(100, (self.processed_records / self.total_records) * 100)
    
    @property
    def is_expired(self):
        """Check if export download has expired"""
        if not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at

# Index definitions for better query performance
from sqlalchemy import Index

# Create composite indexes for common queries
Index('idx_leads_campaign_status', Lead.campaign_id, Lead.status)
Index('idx_leads_campaign_score', Lead.campaign_id, Lead.ai_score.desc())
Index('idx_campaigns_user_status', Campaign.user_id, Campaign.status)
Index('idx_exports_user_status', Export.user_id, Export.status)
