-- PostgreSQL Database Schema for LinkedIn Lead Generation
-- Comprehensive schema with proper indexing, constraints, and relationships

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- For text search

-- Users table for authentication and account management
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    company VARCHAR(255),
    phone VARCHAR(20),
    
    -- Subscription and billing
    subscription_tier VARCHAR(50) DEFAULT 'free' CHECK (subscription_tier IN ('free', 'premium', 'enterprise')),
    credits_remaining INTEGER DEFAULT 100 CHECK (credits_remaining >= 0),
    credits_used INTEGER DEFAULT 0 CHECK (credits_used >= 0),
    
    -- Account status
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    email_notifications BOOLEAN DEFAULT TRUE,
    timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- Security
    last_login TIMESTAMP WITH TIME ZONE,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Campaigns table for lead generation campaigns
CREATE TABLE campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Campaign details
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Search parameters (stored as arrays)
    keywords TEXT[] NOT NULL CHECK (array_length(keywords, 1) > 0),
    target_locations TEXT[] DEFAULT '{}',
    target_industries TEXT[] DEFAULT '{}',
    target_company_sizes TEXT[] DEFAULT '{}',
    target_seniority_levels TEXT[] DEFAULT '{}',
    
    -- Campaign settings
    max_leads INTEGER DEFAULT 1000 CHECK (max_leads > 0 AND max_leads <= 10000),
    include_private_profiles BOOLEAN DEFAULT FALSE,
    
    -- Status and progress
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed', 'failed')),
    leads_found INTEGER DEFAULT 0 CHECK (leads_found >= 0),
    leads_processed INTEGER DEFAULT 0 CHECK (leads_processed >= 0),
    leads_qualified INTEGER DEFAULT 0 CHECK (leads_qualified >= 0),
    
    -- Apify integration
    apify_run_id VARCHAR(255),
    apify_status VARCHAR(50),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Main leads table for LinkedIn profile data
CREATE TABLE leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
    
    -- LinkedIn profile information
    linkedin_url VARCHAR(500) UNIQUE,
    linkedin_id VARCHAR(100),  -- LinkedIn internal ID if available
    full_name VARCHAR(255),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    headline VARCHAR(500),
    summary TEXT,
    
    -- Location information
    location VARCHAR(255),
    country VARCHAR(100),
    city VARCHAR(100),
    region VARCHAR(100),
    
    -- Current position
    current_position VARCHAR(255),
    company VARCHAR(255),
    company_url VARCHAR(500),
    company_size VARCHAR(100),
    company_industry VARCHAR(255),
    company_type VARCHAR(100),  -- Public, Private, Non-profit, etc.
    
    -- Professional details
    industry VARCHAR(255),
    experience_years INTEGER CHECK (experience_years >= 0 AND experience_years <= 70),
    seniority_level VARCHAR(100),
    
    -- Skills and education (stored as JSONB for flexibility)
    skills TEXT[] DEFAULT '{}',
    languages TEXT[] DEFAULT '{}',
    education JSONB DEFAULT '[]',
    experience JSONB DEFAULT '[]',
    certifications JSONB DEFAULT '[]',
    
    -- Contact information
    email VARCHAR(255),
    phone VARCHAR(20),
    website VARCHAR(500),
    twitter_handle VARCHAR(100),
    other_social JSONB DEFAULT '{}',
    
    -- Profile metrics
    connections_count INTEGER CHECK (connections_count >= 0),
    followers_count INTEGER CHECK (followers_count >= 0),
    profile_image_url VARCHAR(500),
    profile_completeness_score DECIMAL(3,2) CHECK (profile_completeness_score >= 0 AND profile_completeness_score <= 1),
    
    -- AI scoring and analysis
    ai_score DECIMAL(3,2) CHECK (ai_score >= 0 AND ai_score <= 1),
    ai_reasoning TEXT,
    ai_criteria_breakdown JSONB DEFAULT '{}',
    ai_model_version VARCHAR(50),
    ai_confidence_level DECIMAL(3,2) CHECK (ai_confidence_level >= 0 AND ai_confidence_level <= 1),
    
    -- Lead management
    status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'qualified', 'contacted', 'responded', 'converted', 'rejected', 'do_not_contact')),
    priority VARCHAR(20) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    source VARCHAR(100) DEFAULT 'linkedin_scraping',
    tags TEXT[] DEFAULT '{}',
    notes TEXT,
    
    -- Engagement tracking
    last_contacted TIMESTAMP WITH TIME ZONE,
    contact_attempts INTEGER DEFAULT 0,
    response_received BOOLEAN DEFAULT FALSE,
    conversion_date TIMESTAMP WITH TIME ZONE,
    
    -- Data quality and validation
    data_quality_score DECIMAL(3,2) CHECK (data_quality_score >= 0 AND data_quality_score <= 1),
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead scoring history for audit trail
CREATE TABLE lead_scoring_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    
    -- Scoring details
    score DECIMAL(3,2) NOT NULL CHECK (score >= 0 AND score <= 1),
    previous_score DECIMAL(3,2),
    reasoning TEXT,
    criteria_breakdown JSONB DEFAULT '{}',
    criteria_weights JSONB DEFAULT '{}',
    
    -- Model information
    model_version VARCHAR(50),
    model_type VARCHAR(100),  -- 'crewai', 'openai', 'custom', etc.
    
    -- Performance metrics
    processing_time_ms INTEGER CHECK (processing_time_ms >= 0),
    confidence_level DECIMAL(3,2) CHECK (confidence_level >= 0 AND confidence_level <= 1),
    
    -- Timestamps
    scored_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead interactions and communication history
CREATE TABLE lead_interactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Interaction details
    interaction_type VARCHAR(50) NOT NULL CHECK (interaction_type IN ('email', 'linkedin_message', 'phone_call', 'meeting', 'note', 'status_change')),
    subject VARCHAR(255),
    content TEXT,
    
    -- Communication details
    direction VARCHAR(20) CHECK (direction IN ('outbound', 'inbound')),
    channel VARCHAR(50),  -- 'email', 'linkedin', 'phone', 'in_person', etc.
    
    -- Status and outcome
    status VARCHAR(50) DEFAULT 'completed' CHECK (status IN ('scheduled', 'completed', 'failed', 'cancelled')),
    outcome VARCHAR(100),  -- 'positive', 'negative', 'neutral', 'no_response', etc.
    
    -- Scheduling
    scheduled_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Export jobs for data export functionality
CREATE TABLE exports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    campaign_id UUID REFERENCES campaigns(id) ON DELETE SET NULL,
    
    -- Export configuration
    format VARCHAR(20) NOT NULL CHECK (format IN ('csv', 'xlsx', 'json', 'pdf')),
    filters JSONB DEFAULT '{}',
    columns TEXT[] DEFAULT '{}',
    
    -- File details
    file_path VARCHAR(500),
    file_size BIGINT CHECK (file_size >= 0),
    download_url VARCHAR(500),
    
    -- Status tracking
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'expired')),
    total_records INTEGER CHECK (total_records >= 0),
    processed_records INTEGER DEFAULT 0 CHECK (processed_records >= 0),
    error_message TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days')
);

-- Lead duplicates tracking
CREATE TABLE lead_duplicates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    original_lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    duplicate_lead_id UUID NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    
    -- Similarity metrics
    similarity_score DECIMAL(3,2) CHECK (similarity_score >= 0 AND similarity_score <= 1),
    matching_fields TEXT[] DEFAULT '{}',
    
    -- Resolution
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'false_positive', 'merged')),
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    
    -- Timestamps
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance optimization
-- Users indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_subscription_tier ON users(subscription_tier);
CREATE INDEX idx_users_created_at ON users(created_at);

-- Campaigns indexes
CREATE INDEX idx_campaigns_user_id ON campaigns(user_id);
CREATE INDEX idx_campaigns_status ON campaigns(status);
CREATE INDEX idx_campaigns_created_at ON campaigns(created_at);
CREATE INDEX idx_campaigns_keywords ON campaigns USING GIN(keywords);

-- Leads indexes (most important for performance)
CREATE INDEX idx_leads_campaign_id ON leads(campaign_id);
CREATE INDEX idx_leads_linkedin_url ON leads(linkedin_url);
CREATE INDEX idx_leads_email ON leads(email);
CREATE INDEX idx_leads_status ON leads(status);
CREATE INDEX idx_leads_ai_score ON leads(ai_score DESC);
CREATE INDEX idx_leads_company ON leads(company);
CREATE INDEX idx_leads_location ON leads(location);
CREATE INDEX idx_leads_scraped_at ON leads(scraped_at);
CREATE INDEX idx_leads_full_name ON leads(full_name);

-- Composite indexes for common queries
CREATE INDEX idx_leads_campaign_status ON leads(campaign_id, status);
CREATE INDEX idx_leads_campaign_score ON leads(campaign_id, ai_score DESC);
CREATE INDEX idx_leads_status_score ON leads(status, ai_score DESC);

-- Text search indexes
CREATE INDEX idx_leads_full_name_trgm ON leads USING GIN(full_name gin_trgm_ops);
CREATE INDEX idx_leads_headline_trgm ON leads USING GIN(headline gin_trgm_ops);
CREATE INDEX idx_leads_company_trgm ON leads USING GIN(company gin_trgm_ops);

-- JSONB indexes for flexible queries
CREATE INDEX idx_leads_skills ON leads USING GIN(skills);
CREATE INDEX idx_leads_education ON leads USING GIN(education);
CREATE INDEX idx_leads_ai_criteria ON leads USING GIN(ai_criteria_breakdown);

-- Scoring history indexes
CREATE INDEX idx_scoring_history_lead_id ON lead_scoring_history(lead_id);
CREATE INDEX idx_scoring_history_scored_at ON lead_scoring_history(scored_at);

-- Interactions indexes
CREATE INDEX idx_interactions_lead_id ON lead_interactions(lead_id);
CREATE INDEX idx_interactions_user_id ON lead_interactions(user_id);
CREATE INDEX idx_interactions_type ON lead_interactions(interaction_type);
CREATE INDEX idx_interactions_created_at ON lead_interactions(created_at);

-- Exports indexes
CREATE INDEX idx_exports_user_id ON exports(user_id);
CREATE INDEX idx_exports_status ON exports(status);
CREATE INDEX idx_exports_created_at ON exports(created_at);

-- Triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_campaigns_updated_at BEFORE UPDATE ON campaigns FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_leads_updated_at BEFORE UPDATE ON leads FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for common queries
CREATE VIEW qualified_leads AS
SELECT 
    l.*,
    c.name as campaign_name,
    u.email as user_email
FROM leads l
JOIN campaigns c ON l.campaign_id = c.id
JOIN users u ON c.user_id = u.id
WHERE l.ai_score >= 0.7 AND l.status IN ('new', 'qualified');

CREATE VIEW campaign_stats AS
SELECT 
    c.id,
    c.name,
    c.status,
    COUNT(l.id) as total_leads,
    COUNT(CASE WHEN l.ai_score >= 0.7 THEN 1 END) as qualified_leads,
    COUNT(CASE WHEN l.status = 'contacted' THEN 1 END) as contacted_leads,
    COUNT(CASE WHEN l.status = 'converted' THEN 1 END) as converted_leads,
    AVG(l.ai_score) as avg_score,
    MAX(l.scraped_at) as last_scraped
FROM campaigns c
LEFT JOIN leads l ON c.id = l.campaign_id
GROUP BY c.id, c.name, c.status;
