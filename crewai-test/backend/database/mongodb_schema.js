// MongoDB Schema Design for LinkedIn Lead Generation
// Using MongoDB 6.0+ with proper indexing and validation

// Database: linkedin_leads
use linkedin_leads;

// Users Collection
db.createCollection("users", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["email", "password_hash", "subscription_tier", "credits_remaining"],
      properties: {
        _id: { bsonType: "objectId" },
        email: { 
          bsonType: "string", 
          pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
          description: "Valid email address required"
        },
        password_hash: { bsonType: "string", minLength: 60 },
        first_name: { bsonType: "string", maxLength: 100 },
        last_name: { bsonType: "string", maxLength: 100 },
        company: { bsonType: "string", maxLength: 255 },
        phone: { bsonType: "string", maxLength: 20 },
        
        // Subscription and billing
        subscription_tier: { 
          bsonType: "string", 
          enum: ["free", "premium", "enterprise"],
          description: "Must be one of: free, premium, enterprise"
        },
        credits_remaining: { bsonType: "int", minimum: 0 },
        credits_used: { bsonType: "int", minimum: 0 },
        
        // Account status
        is_active: { bsonType: "bool" },
        is_verified: { bsonType: "bool" },
        email_notifications: { bsonType: "bool" },
        timezone: { bsonType: "string", maxLength: 50 },
        
        // Security
        last_login: { bsonType: "date" },
        failed_login_attempts: { bsonType: "int", minimum: 0 },
        locked_until: { bsonType: "date" },
        
        // Timestamps
        created_at: { bsonType: "date" },
        updated_at: { bsonType: "date" }
      }
    }
  }
});

// Campaigns Collection
db.createCollection("campaigns", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["user_id", "name", "keywords", "max_leads"],
      properties: {
        _id: { bsonType: "objectId" },
        user_id: { bsonType: "objectId" },
        
        // Campaign details
        name: { bsonType: "string", minLength: 1, maxLength: 255 },
        description: { bsonType: "string" },
        
        // Search parameters
        keywords: { 
          bsonType: "array", 
          minItems: 1,
          items: { bsonType: "string" }
        },
        target_locations: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        target_industries: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        target_company_sizes: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        target_seniority_levels: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        
        // Campaign settings
        max_leads: { bsonType: "int", minimum: 1, maximum: 10000 },
        include_private_profiles: { bsonType: "bool" },
        
        // Status and progress
        status: { 
          bsonType: "string", 
          enum: ["draft", "active", "paused", "completed", "failed"]
        },
        leads_found: { bsonType: "int", minimum: 0 },
        leads_processed: { bsonType: "int", minimum: 0 },
        leads_qualified: { bsonType: "int", minimum: 0 },
        
        // Apify integration
        apify_run_id: { bsonType: "string" },
        apify_status: { bsonType: "string" },
        
        // Timestamps
        created_at: { bsonType: "date" },
        updated_at: { bsonType: "date" },
        started_at: { bsonType: "date" },
        completed_at: { bsonType: "date" }
      }
    }
  }
});

// Leads Collection (Main collection for LinkedIn profiles)
db.createCollection("leads", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["campaign_id", "scraped_at"],
      properties: {
        _id: { bsonType: "objectId" },
        campaign_id: { bsonType: "objectId" },
        
        // LinkedIn profile information
        linkedin_url: { bsonType: "string", maxLength: 500 },
        linkedin_id: { bsonType: "string", maxLength: 100 },
        full_name: { bsonType: "string", maxLength: 255 },
        first_name: { bsonType: "string", maxLength: 100 },
        last_name: { bsonType: "string", maxLength: 100 },
        headline: { bsonType: "string", maxLength: 500 },
        summary: { bsonType: "string" },
        
        // Location information
        location: { bsonType: "string", maxLength: 255 },
        country: { bsonType: "string", maxLength: 100 },
        city: { bsonType: "string", maxLength: 100 },
        region: { bsonType: "string", maxLength: 100 },
        
        // Current position
        current_position: { bsonType: "string", maxLength: 255 },
        company: { bsonType: "string", maxLength: 255 },
        company_url: { bsonType: "string", maxLength: 500 },
        company_size: { bsonType: "string", maxLength: 100 },
        company_industry: { bsonType: "string", maxLength: 255 },
        company_type: { bsonType: "string", maxLength: 100 },
        
        // Professional details
        industry: { bsonType: "string", maxLength: 255 },
        experience_years: { bsonType: "int", minimum: 0, maximum: 70 },
        seniority_level: { bsonType: "string", maxLength: 100 },
        
        // Skills and education (flexible objects)
        skills: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        languages: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        education: { bsonType: "array" },
        experience: { bsonType: "array" },
        certifications: { bsonType: "array" },
        
        // Contact information
        email: { bsonType: "string", maxLength: 255 },
        phone: { bsonType: "string", maxLength: 20 },
        website: { bsonType: "string", maxLength: 500 },
        twitter_handle: { bsonType: "string", maxLength: 100 },
        other_social: { bsonType: "object" },
        
        // Profile metrics
        connections_count: { bsonType: "int", minimum: 0 },
        followers_count: { bsonType: "int", minimum: 0 },
        profile_image_url: { bsonType: "string", maxLength: 500 },
        profile_completeness_score: { bsonType: "double", minimum: 0, maximum: 1 },
        
        // AI scoring and analysis
        ai_score: { bsonType: "double", minimum: 0, maximum: 1 },
        ai_reasoning: { bsonType: "string" },
        ai_criteria_breakdown: { bsonType: "object" },
        ai_model_version: { bsonType: "string", maxLength: 50 },
        ai_confidence_level: { bsonType: "double", minimum: 0, maximum: 1 },
        
        // Lead management
        status: { 
          bsonType: "string", 
          enum: ["new", "qualified", "contacted", "responded", "converted", "rejected", "do_not_contact"]
        },
        priority: { 
          bsonType: "string", 
          enum: ["low", "medium", "high", "urgent"]
        },
        source: { bsonType: "string", maxLength: 100 },
        tags: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        notes: { bsonType: "string" },
        
        // Engagement tracking
        last_contacted: { bsonType: "date" },
        contact_attempts: { bsonType: "int", minimum: 0 },
        response_received: { bsonType: "bool" },
        conversion_date: { bsonType: "date" },
        
        // Data quality and validation
        data_quality_score: { bsonType: "double", minimum: 0, maximum: 1 },
        is_verified: { bsonType: "bool" },
        verification_date: { bsonType: "date" },
        
        // Timestamps
        scraped_at: { bsonType: "date" },
        processed_at: { bsonType: "date" },
        created_at: { bsonType: "date" },
        updated_at: { bsonType: "date" }
      }
    }
  }
});

// Lead Scoring History Collection
db.createCollection("lead_scoring_history", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["lead_id", "score", "scored_at"],
      properties: {
        _id: { bsonType: "objectId" },
        lead_id: { bsonType: "objectId" },
        
        // Scoring details
        score: { bsonType: "double", minimum: 0, maximum: 1 },
        previous_score: { bsonType: "double", minimum: 0, maximum: 1 },
        reasoning: { bsonType: "string" },
        criteria_breakdown: { bsonType: "object" },
        criteria_weights: { bsonType: "object" },
        
        // Model information
        model_version: { bsonType: "string", maxLength: 50 },
        model_type: { bsonType: "string", maxLength: 100 },
        
        // Performance metrics
        processing_time_ms: { bsonType: "int", minimum: 0 },
        confidence_level: { bsonType: "double", minimum: 0, maximum: 1 },
        
        // Timestamps
        scored_at: { bsonType: "date" }
      }
    }
  }
});

// Lead Interactions Collection
db.createCollection("lead_interactions", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["lead_id", "user_id", "interaction_type", "created_at"],
      properties: {
        _id: { bsonType: "objectId" },
        lead_id: { bsonType: "objectId" },
        user_id: { bsonType: "objectId" },
        
        // Interaction details
        interaction_type: { 
          bsonType: "string", 
          enum: ["email", "linkedin_message", "phone_call", "meeting", "note", "status_change"]
        },
        subject: { bsonType: "string", maxLength: 255 },
        content: { bsonType: "string" },
        
        // Communication details
        direction: { bsonType: "string", enum: ["outbound", "inbound"] },
        channel: { bsonType: "string", maxLength: 50 },
        
        // Status and outcome
        status: { 
          bsonType: "string", 
          enum: ["scheduled", "completed", "failed", "cancelled"]
        },
        outcome: { bsonType: "string", maxLength: 100 },
        
        // Scheduling
        scheduled_at: { bsonType: "date" },
        completed_at: { bsonType: "date" },
        
        // Metadata
        metadata: { bsonType: "object" },
        
        // Timestamps
        created_at: { bsonType: "date" }
      }
    }
  }
});

// Exports Collection
db.createCollection("exports", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["user_id", "format", "status"],
      properties: {
        _id: { bsonType: "objectId" },
        user_id: { bsonType: "objectId" },
        campaign_id: { bsonType: "objectId" },
        
        // Export configuration
        format: { 
          bsonType: "string", 
          enum: ["csv", "xlsx", "json", "pdf"]
        },
        filters: { bsonType: "object" },
        columns: { 
          bsonType: "array",
          items: { bsonType: "string" }
        },
        
        // File details
        file_path: { bsonType: "string", maxLength: 500 },
        file_size: { bsonType: "long", minimum: 0 },
        download_url: { bsonType: "string", maxLength: 500 },
        
        // Status tracking
        status: { 
          bsonType: "string", 
          enum: ["pending", "processing", "completed", "failed", "expired"]
        },
        total_records: { bsonType: "int", minimum: 0 },
        processed_records: { bsonType: "int", minimum: 0 },
        error_message: { bsonType: "string" },
        
        // Timestamps
        created_at: { bsonType: "date" },
        started_at: { bsonType: "date" },
        completed_at: { bsonType: "date" },
        expires_at: { bsonType: "date" }
      }
    }
  }
});

// Create Indexes for Performance

// Users indexes
db.users.createIndex({ "email": 1 }, { unique: true });
db.users.createIndex({ "subscription_tier": 1 });
db.users.createIndex({ "created_at": -1 });

// Campaigns indexes
db.campaigns.createIndex({ "user_id": 1 });
db.campaigns.createIndex({ "status": 1 });
db.campaigns.createIndex({ "created_at": -1 });
db.campaigns.createIndex({ "keywords": 1 });
db.campaigns.createIndex({ "user_id": 1, "status": 1 });

// Leads indexes (critical for performance)
db.leads.createIndex({ "campaign_id": 1 });
db.leads.createIndex({ "linkedin_url": 1 }, { unique: true, sparse: true });
db.leads.createIndex({ "email": 1 }, { sparse: true });
db.leads.createIndex({ "status": 1 });
db.leads.createIndex({ "ai_score": -1 });
db.leads.createIndex({ "company": 1 });
db.leads.createIndex({ "location": 1 });
db.leads.createIndex({ "scraped_at": -1 });
db.leads.createIndex({ "full_name": 1 });

// Compound indexes for common queries
db.leads.createIndex({ "campaign_id": 1, "status": 1 });
db.leads.createIndex({ "campaign_id": 1, "ai_score": -1 });
db.leads.createIndex({ "status": 1, "ai_score": -1 });

// Text search indexes
db.leads.createIndex({ 
  "full_name": "text", 
  "headline": "text", 
  "company": "text",
  "summary": "text"
});

// Array field indexes
db.leads.createIndex({ "skills": 1 });
db.leads.createIndex({ "tags": 1 });

// Scoring history indexes
db.lead_scoring_history.createIndex({ "lead_id": 1 });
db.lead_scoring_history.createIndex({ "scored_at": -1 });
db.lead_scoring_history.createIndex({ "lead_id": 1, "scored_at": -1 });

// Interactions indexes
db.lead_interactions.createIndex({ "lead_id": 1 });
db.lead_interactions.createIndex({ "user_id": 1 });
db.lead_interactions.createIndex({ "interaction_type": 1 });
db.lead_interactions.createIndex({ "created_at": -1 });
db.lead_interactions.createIndex({ "lead_id": 1, "created_at": -1 });

// Exports indexes
db.exports.createIndex({ "user_id": 1 });
db.exports.createIndex({ "status": 1 });
db.exports.createIndex({ "created_at": -1 });
db.exports.createIndex({ "expires_at": 1 });

// TTL index for automatic cleanup of expired exports
db.exports.createIndex({ "expires_at": 1 }, { expireAfterSeconds: 0 });

// Sample data insertion for testing
db.users.insertOne({
  email: "<EMAIL>",
  password_hash: "$2b$12$example_hash",
  first_name: "Test",
  last_name: "User",
  subscription_tier: "premium",
  credits_remaining: 1000,
  credits_used: 0,
  is_active: true,
  is_verified: true,
  email_notifications: true,
  timezone: "UTC",
  created_at: new Date(),
  updated_at: new Date()
});

// Print schema information
print("MongoDB schema created successfully!");
print("Collections created:");
print("- users");
print("- campaigns"); 
print("- leads");
print("- lead_scoring_history");
print("- lead_interactions");
print("- exports");
print("\nIndexes created for optimal query performance.");
print("Schema validation rules applied to ensure data integrity.");
