# MongoDB Lead Data Processor
# Service for processing and saving Apify LinkedIn results to MongoDB

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.errors import Duplicate<PERSON>eyError, BulkWriteError
from bson import ObjectId
import re

from services.apify_client import LinkedInProfile
from core.config import settings

logger = logging.getLogger(__name__)

class MongoLeadDataProcessor:
    """
    Processes LinkedIn profile data from Apify and saves to MongoDB
    Handles data validation, deduplication, and enrichment
    """
    
    def __init__(self, connection_string: str = None, database_name: str = "linkedin_leads"):
        """
        Initialize MongoDB lead processor
        
        Args:
            connection_string: MongoDB connection string
            database_name: Database name
        """
        self.connection_string = connection_string or settings.MONGODB_URL
        self.database_name = database_name
        self.client = None
        self.db = None
        
        # Data quality weights
        self.data_quality_weights = {
            'has_name': 0.2,
            'has_headline': 0.15,
            'has_company': 0.2,
            'has_location': 0.1,
            'has_experience': 0.15,
            'has_skills': 0.1,
            'has_contact_info': 0.1
        }
        
        self.duplicate_threshold = 0.85
    
    def connect(self):
        """Establish MongoDB connection"""
        try:
            self.client = MongoClient(self.connection_string)
            self.db = self.client[self.database_name]
            
            # Test connection
            self.client.admin.command('ping')
            logger.info(f"Connected to MongoDB: {self.database_name}")
            
        except Exception as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise
    
    def disconnect(self):
        """Close MongoDB connection"""
        if self.client:
            self.client.close()
            logger.info("Disconnected from MongoDB")
    
    async def process_apify_results(
        self, 
        campaign_id: str,
        apify_profiles: List[LinkedInProfile]
    ) -> Dict[str, Any]:
        """
        Process Apify LinkedIn scraping results and save to MongoDB
        
        Args:
            campaign_id: Campaign ObjectId as string
            apify_profiles: List of LinkedIn profiles from Apify
            
        Returns:
            Dict with processing statistics
        """
        if not self.db:
            self.connect()
        
        try:
            logger.info(f"Processing {len(apify_profiles)} LinkedIn profiles for campaign {campaign_id}")
            
            # Get campaign info
            campaign = self.db.campaigns.find_one({"_id": ObjectId(campaign_id)})
            if not campaign:
                raise ValueError(f"Campaign {campaign_id} not found")
            
            # Processing statistics
            stats = {
                'total_profiles': len(apify_profiles),
                'leads_created': 0,
                'leads_updated': 0,
                'duplicates_found': 0,
                'validation_failures': 0,
                'processing_errors': 0
            }
            
            # Process profiles in batches for better performance
            batch_size = 100
            for i in range(0, len(apify_profiles), batch_size):
                batch = apify_profiles[i:i + batch_size]
                batch_stats = await self._process_profile_batch(campaign_id, batch)
                
                # Aggregate statistics
                for key in stats:
                    if key != 'total_profiles':
                        stats[key] += batch_stats[key]
            
            # Update campaign statistics
            self.db.campaigns.update_one(
                {"_id": ObjectId(campaign_id)},
                {
                    "$set": {
                        "leads_found": stats['leads_created'] + stats['leads_updated'],
                        "leads_processed": stats['total_profiles'],
                        "updated_at": datetime.utcnow()
                    }
                }
            )
            
            logger.info(f"Processing complete: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error processing Apify results: {e}")
            raise
    
    async def _process_profile_batch(
        self, 
        campaign_id: str, 
        profiles: List[LinkedInProfile]
    ) -> Dict[str, Any]:
        """
        Process a batch of LinkedIn profiles
        
        Args:
            campaign_id: Campaign ObjectId as string
            profiles: Batch of LinkedIn profiles
            
        Returns:
            Dict with batch processing statistics
        """
        stats = {
            'leads_created': 0,
            'leads_updated': 0,
            'duplicates_found': 0,
            'validation_failures': 0,
            'processing_errors': 0
        }
        
        # Prepare bulk operations
        bulk_operations = []
        
        for profile in profiles:
            try:
                # Validate profile data
                if not self._validate_profile_data(profile):
                    stats['validation_failures'] += 1
                    continue
                
                # Check for existing lead
                existing_lead = await self._find_existing_lead(profile)
                
                if existing_lead:
                    # Check if it's a duplicate from different campaign
                    if str(existing_lead['campaign_id']) != campaign_id:
                        stats['duplicates_found'] += 1
                        continue
                    
                    # Update existing lead
                    update_data = self._transform_profile_to_lead_update(profile)
                    if update_data:
                        bulk_operations.append({
                            'update_one': {
                                'filter': {'_id': existing_lead['_id']},
                                'update': {'$set': update_data}
                            }
                        })
                        stats['leads_updated'] += 1
                else:
                    # Create new lead
                    lead_data = self._transform_profile_to_lead(campaign_id, profile)
                    bulk_operations.append({
                        'insert_one': {
                            'document': lead_data
                        }
                    })
                    stats['leads_created'] += 1
                    
            except Exception as e:
                logger.warning(f"Error processing profile {profile.url}: {e}")
                stats['processing_errors'] += 1
                continue
        
        # Execute bulk operations
        if bulk_operations:
            try:
                result = self.db.leads.bulk_write(bulk_operations, ordered=False)
                logger.debug(f"Bulk write result: {result.bulk_api_result}")
                
            except BulkWriteError as e:
                logger.warning(f"Bulk write errors: {e.details}")
                # Continue processing despite some errors
        
        return stats
    
    def _validate_profile_data(self, profile: LinkedInProfile) -> bool:
        """
        Validate LinkedIn profile data quality
        
        Args:
            profile: LinkedIn profile data
            
        Returns:
            bool: True if profile passes validation
        """
        # Minimum requirements
        if not profile.url:
            logger.debug("Profile rejected: No LinkedIn URL")
            return False
        
        if not profile.full_name and not profile.headline:
            logger.debug("Profile rejected: No name or headline")
            return False
        
        # Check for suspicious patterns
        if profile.full_name:
            suspicious_patterns = [
                r'^LinkedIn Member$',
                r'^Private Profile$',
                r'^[A-Z]{2,}\s[A-Z]{2,}$',  # All caps names
                r'^\d+$',  # Only numbers
            ]
            
            for pattern in suspicious_patterns:
                if re.match(pattern, profile.full_name, re.IGNORECASE):
                    logger.debug(f"Profile rejected: Suspicious name pattern - {profile.full_name}")
                    return False
        
        return True
    
    async def _find_existing_lead(self, profile: LinkedInProfile) -> Optional[Dict[str, Any]]:
        """
        Find existing lead by LinkedIn URL or similarity
        
        Args:
            profile: LinkedIn profile data
            
        Returns:
            Optional[Dict]: Existing lead document if found
        """
        # First, check by LinkedIn URL (exact match)
        if profile.url:
            existing_lead = self.db.leads.find_one({"linkedin_url": profile.url})
            if existing_lead:
                return existing_lead
        
        # Check for potential duplicates by name and company
        if profile.full_name:
            potential_duplicates = self.db.leads.find({
                "$text": {"$search": profile.full_name}
            }).limit(10)
            
            for lead in potential_duplicates:
                similarity_score = self._calculate_similarity_mongo(profile, lead)
                if similarity_score >= self.duplicate_threshold:
                    return lead
        
        return None
    
    def _calculate_similarity_mongo(self, profile: LinkedInProfile, lead: Dict[str, Any]) -> float:
        """
        Calculate similarity score between profile and existing lead
        
        Args:
            profile: LinkedIn profile data
            lead: Existing lead document
            
        Returns:
            float: Similarity score (0.0 to 1.0)
        """
        score = 0.0
        total_weight = 0.0
        
        # Name similarity (weight: 0.4)
        if profile.full_name and lead.get('full_name'):
            name_similarity = self._string_similarity(
                profile.full_name.lower(), 
                lead['full_name'].lower()
            )
            score += name_similarity * 0.4
            total_weight += 0.4
        
        # Company similarity (weight: 0.3)
        if profile.company and lead.get('company'):
            company_similarity = self._string_similarity(
                profile.company.lower(), 
                lead['company'].lower()
            )
            score += company_similarity * 0.3
            total_weight += 0.3
        
        # Location similarity (weight: 0.2)
        if profile.location and lead.get('location'):
            location_similarity = self._string_similarity(
                profile.location.lower(), 
                lead['location'].lower()
            )
            score += location_similarity * 0.2
            total_weight += 0.2
        
        # Headline similarity (weight: 0.1)
        if profile.headline and lead.get('headline'):
            headline_similarity = self._string_similarity(
                profile.headline.lower(), 
                lead['headline'].lower()
            )
            score += headline_similarity * 0.1
            total_weight += 0.1
        
        return score / total_weight if total_weight > 0 else 0.0
    
    def _string_similarity(self, str1: str, str2: str) -> float:
        """Calculate string similarity using simple ratio"""
        if str1 == str2:
            return 1.0
        
        # Simple Jaccard similarity for MongoDB context
        set1 = set(str1.lower().split())
        set2 = set(str2.lower().split())
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        return intersection / union if union > 0 else 0.0
    
    def _transform_profile_to_lead(
        self, 
        campaign_id: str, 
        profile: LinkedInProfile
    ) -> Dict[str, Any]:
        """
        Transform LinkedIn profile to MongoDB lead document
        
        Args:
            campaign_id: Campaign ObjectId as string
            profile: LinkedIn profile data
            
        Returns:
            Dict: Lead document for MongoDB insertion
        """
        # Extract first and last name
        first_name, last_name = self._parse_full_name(profile.full_name)
        
        # Calculate scores
        data_quality_score = self._calculate_data_quality_score(profile)
        profile_completeness = self._calculate_profile_completeness(profile)
        
        # Parse location
        location_parts = self._parse_location(profile.location)
        
        lead_document = {
            "campaign_id": ObjectId(campaign_id),
            
            # LinkedIn profile information
            "linkedin_url": profile.url,
            "linkedin_id": None,  # Could be extracted from URL
            "full_name": profile.full_name,
            "first_name": first_name,
            "last_name": last_name,
            "headline": profile.headline,
            "summary": profile.summary,
            
            # Location information
            "location": profile.location,
            "country": location_parts.get('country'),
            "city": location_parts.get('city'),
            "region": location_parts.get('region'),
            
            # Current position
            "current_position": profile.current_position,
            "company": profile.company,
            "company_url": profile.company_url,
            "company_size": profile.company_size,
            "company_industry": profile.industry,
            "company_type": None,
            
            # Professional details
            "industry": profile.industry,
            "experience_years": self._calculate_experience_years(profile.experience),
            "seniority_level": None,  # Could be inferred from title
            
            # Skills and education
            "skills": profile.skills or [],
            "languages": profile.languages or [],
            "education": profile.education or [],
            "experience": profile.experience or [],
            "certifications": profile.certifications or [],
            
            # Contact information
            "email": profile.contact_info.get('email') if profile.contact_info else None,
            "phone": profile.contact_info.get('phone') if profile.contact_info else None,
            "website": profile.contact_info.get('website') if profile.contact_info else None,
            "twitter_handle": profile.contact_info.get('twitter') if profile.contact_info else None,
            "other_social": {
                k: v for k, v in (profile.contact_info or {}).items() 
                if k not in ['email', 'phone', 'website', 'twitter']
            },
            
            # Profile metrics
            "connections_count": profile.connections_count,
            "followers_count": profile.followers_count,
            "profile_image_url": profile.profile_image_url,
            "profile_completeness_score": profile_completeness,
            
            # AI scoring (to be filled later)
            "ai_score": None,
            "ai_reasoning": None,
            "ai_criteria_breakdown": {},
            "ai_model_version": None,
            "ai_confidence_level": None,
            
            # Lead management
            "status": "new",
            "priority": "medium",
            "source": "linkedin_scraping",
            "tags": [],
            "notes": None,
            
            # Engagement tracking
            "last_contacted": None,
            "contact_attempts": 0,
            "response_received": False,
            "conversion_date": None,
            
            # Data quality
            "data_quality_score": data_quality_score,
            "is_verified": False,
            "verification_date": None,
            
            # Timestamps
            "scraped_at": profile.scraped_at,
            "processed_at": None,
            "created_at": datetime.utcnow(),
            "updated_at": datetime.utcnow()
        }
        
        return lead_document
    
    def _transform_profile_to_lead_update(self, profile: LinkedInProfile) -> Dict[str, Any]:
        """
        Transform LinkedIn profile to update document for existing lead
        
        Args:
            profile: LinkedIn profile data
            
        Returns:
            Dict: Update document for MongoDB
        """
        update_data = {
            "updated_at": datetime.utcnow(),
            "scraped_at": profile.scraped_at
        }
        
        # Update fields only if they have values and current field is empty
        # This would require checking existing document first in a real implementation
        
        return update_data
    
    def _parse_full_name(self, full_name: str) -> Tuple[Optional[str], Optional[str]]:
        """Parse full name into first and last name components"""
        if not full_name:
            return None, None
        
        name_parts = full_name.strip().split()
        if len(name_parts) == 0:
            return None, None
        elif len(name_parts) == 1:
            return name_parts[0], None
        else:
            return name_parts[0], ' '.join(name_parts[1:])
    
    def _parse_location(self, location: str) -> Dict[str, Optional[str]]:
        """Parse location string into components"""
        if not location:
            return {'country': None, 'city': None, 'region': None}
        
        parts = [part.strip() for part in location.split(',')]
        
        if len(parts) == 1:
            return {'country': None, 'city': parts[0], 'region': None}
        elif len(parts) == 2:
            return {'country': parts[1], 'city': parts[0], 'region': None}
        elif len(parts) >= 3:
            return {'country': parts[-1], 'city': parts[0], 'region': parts[1]}
        
        return {'country': None, 'city': None, 'region': None}
    
    def _calculate_experience_years(self, experience: List[Dict[str, Any]]) -> Optional[int]:
        """Calculate total years of experience"""
        if not experience:
            return None
        
        total_months = 0
        for exp in experience:
            try:
                # Simple estimation - could be enhanced with proper date parsing
                total_months += 24  # Assume 2 years per position
            except:
                continue
        
        return total_months // 12 if total_months > 0 else None
    
    def _calculate_data_quality_score(self, profile: LinkedInProfile) -> float:
        """Calculate data quality score"""
        score = 0.0
        
        if profile.full_name:
            score += self.data_quality_weights['has_name']
        if profile.headline:
            score += self.data_quality_weights['has_headline']
        if profile.company:
            score += self.data_quality_weights['has_company']
        if profile.location:
            score += self.data_quality_weights['has_location']
        if profile.experience and len(profile.experience) > 0:
            score += self.data_quality_weights['has_experience']
        if profile.skills and len(profile.skills) > 0:
            score += self.data_quality_weights['has_skills']
        if profile.contact_info and any(profile.contact_info.values()):
            score += self.data_quality_weights['has_contact_info']
        
        return round(score, 2)
    
    def _calculate_profile_completeness(self, profile: LinkedInProfile) -> float:
        """Calculate LinkedIn profile completeness score"""
        completeness_factors = [
            bool(profile.full_name),
            bool(profile.headline),
            bool(profile.summary),
            bool(profile.company),
            bool(profile.location),
            bool(profile.experience and len(profile.experience) > 0),
            bool(profile.education and len(profile.education) > 0),
            bool(profile.skills and len(profile.skills) > 0),
            bool(profile.profile_image_url),
            bool(profile.connections_count and profile.connections_count > 0)
        ]
        
        return sum(completeness_factors) / len(completeness_factors)
    
    def get_campaign_stats(self, campaign_id: str) -> Dict[str, Any]:
        """Get campaign statistics from MongoDB"""
        pipeline = [
            {"$match": {"campaign_id": ObjectId(campaign_id)}},
            {"$group": {
                "_id": None,
                "total_leads": {"$sum": 1},
                "qualified_leads": {
                    "$sum": {"$cond": [{"$gte": ["$ai_score", 0.7]}, 1, 0]}
                },
                "contacted_leads": {
                    "$sum": {"$cond": [{"$eq": ["$status", "contacted"]}, 1, 0]}
                },
                "converted_leads": {
                    "$sum": {"$cond": [{"$eq": ["$status", "converted"]}, 1, 0]}
                },
                "avg_score": {"$avg": "$ai_score"},
                "avg_data_quality": {"$avg": "$data_quality_score"}
            }}
        ]
        
        result = list(self.db.leads.aggregate(pipeline))
        return result[0] if result else {}

# Global instance
mongo_lead_processor = MongoLeadDataProcessor()
