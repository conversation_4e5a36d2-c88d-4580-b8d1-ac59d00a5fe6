# Lead Data Processor
# Service for processing and saving Apify LinkedIn scraping results to database

import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
import uuid

from models.database import Lead, Campaign, User, ScoringHistory
from services.apify_client import LinkedInProfile
from core.database import get_db
from core.config import settings

logger = logging.getLogger(__name__)

class LeadDataProcessor:
    """
    Processes LinkedIn profile data from Apify and saves to database
    Handles data validation, deduplication, and enrichment
    """
    
    def __init__(self):
        self.duplicate_threshold = 0.85  # Similarity threshold for duplicate detection
        self.data_quality_weights = {
            'has_name': 0.2,
            'has_headline': 0.15,
            'has_company': 0.2,
            'has_location': 0.1,
            'has_experience': 0.15,
            'has_skills': 0.1,
            'has_contact_info': 0.1
        }
    
    async def process_apify_results(
        self, 
        campaign_id: uuid.UUID,
        apify_profiles: List[LinkedInProfile],
        db: Session = None
    ) -> Dict[str, Any]:
        """
        Process Apify LinkedIn scraping results and save to database
        
        Args:
            campaign_id: Campaign UUID
            apify_profiles: List of LinkedIn profiles from Apify
            db: Database session (optional)
            
        Returns:
            Dict with processing statistics
        """
        if not db:
            db = next(get_db())
            close_db = True
        else:
            close_db = False
        
        try:
            logger.info(f"Processing {len(apify_profiles)} LinkedIn profiles for campaign {campaign_id}")
            
            # Get campaign info
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if not campaign:
                raise ValueError(f"Campaign {campaign_id} not found")
            
            # Processing statistics
            stats = {
                'total_profiles': len(apify_profiles),
                'leads_created': 0,
                'leads_updated': 0,
                'duplicates_found': 0,
                'validation_failures': 0,
                'processing_errors': 0
            }
            
            # Process each profile
            for profile in apify_profiles:
                try:
                    result = await self._process_single_profile(
                        campaign_id, profile, db
                    )
                    stats[result] += 1
                    
                except Exception as e:
                    logger.warning(f"Error processing profile {profile.url}: {e}")
                    stats['processing_errors'] += 1
                    continue
            
            # Update campaign statistics
            campaign.leads_found = stats['leads_created'] + stats['leads_updated']
            campaign.leads_processed = stats['total_profiles']
            campaign.updated_at = datetime.utcnow()
            
            db.commit()
            
            logger.info(f"Processing complete: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"Error processing Apify results: {e}")
            db.rollback()
            raise
        finally:
            if close_db:
                db.close()
    
    async def _process_single_profile(
        self, 
        campaign_id: uuid.UUID, 
        profile: LinkedInProfile, 
        db: Session
    ) -> str:
        """
        Process a single LinkedIn profile
        
        Args:
            campaign_id: Campaign UUID
            profile: LinkedIn profile data
            db: Database session
            
        Returns:
            str: Processing result ('leads_created', 'leads_updated', 'duplicates_found', 'validation_failures')
        """
        # Validate profile data
        if not self._validate_profile_data(profile):
            return 'validation_failures'
        
        # Check for existing lead by LinkedIn URL
        existing_lead = None
        if profile.url:
            existing_lead = db.query(Lead).filter(
                Lead.linkedin_url == profile.url
            ).first()
        
        # If no exact match, check for potential duplicates
        if not existing_lead:
            potential_duplicate = await self._find_potential_duplicate(profile, db)
            if potential_duplicate:
                logger.info(f"Potential duplicate found for {profile.full_name}")
                return 'duplicates_found'
        
        # Transform profile to lead data
        lead_data = self._transform_profile_to_lead(campaign_id, profile)
        
        if existing_lead:
            # Update existing lead
            self._update_existing_lead(existing_lead, lead_data, db)
            return 'leads_updated'
        else:
            # Create new lead
            self._create_new_lead(lead_data, db)
            return 'leads_created'
    
    def _validate_profile_data(self, profile: LinkedInProfile) -> bool:
        """
        Validate LinkedIn profile data quality
        
        Args:
            profile: LinkedIn profile data
            
        Returns:
            bool: True if profile passes validation
        """
        # Minimum requirements
        if not profile.url:
            logger.debug("Profile rejected: No LinkedIn URL")
            return False
        
        if not profile.full_name and not profile.headline:
            logger.debug("Profile rejected: No name or headline")
            return False
        
        # Check for suspicious patterns
        if profile.full_name:
            # Reject profiles with suspicious names
            suspicious_patterns = [
                r'^LinkedIn Member$',
                r'^Private Profile$',
                r'^[A-Z]{2,}\s[A-Z]{2,}$',  # All caps names
                r'^\d+$',  # Only numbers
            ]
            
            for pattern in suspicious_patterns:
                if re.match(pattern, profile.full_name, re.IGNORECASE):
                    logger.debug(f"Profile rejected: Suspicious name pattern - {profile.full_name}")
                    return False
        
        return True
    
    async def _find_potential_duplicate(
        self, 
        profile: LinkedInProfile, 
        db: Session
    ) -> Optional[Lead]:
        """
        Find potential duplicate leads using fuzzy matching
        
        Args:
            profile: LinkedIn profile data
            db: Database session
            
        Returns:
            Optional[Lead]: Potential duplicate lead if found
        """
        if not profile.full_name:
            return None
        
        # Search for similar names and companies
        similar_leads = db.query(Lead).filter(
            Lead.full_name.ilike(f"%{profile.full_name}%")
        ).limit(10).all()
        
        for lead in similar_leads:
            similarity_score = self._calculate_similarity(profile, lead)
            if similarity_score >= self.duplicate_threshold:
                return lead
        
        return None
    
    def _calculate_similarity(self, profile: LinkedInProfile, lead: Lead) -> float:
        """
        Calculate similarity score between profile and existing lead
        
        Args:
            profile: LinkedIn profile data
            lead: Existing lead in database
            
        Returns:
            float: Similarity score (0.0 to 1.0)
        """
        score = 0.0
        total_weight = 0.0
        
        # Name similarity (weight: 0.4)
        if profile.full_name and lead.full_name:
            name_similarity = self._string_similarity(
                profile.full_name.lower(), 
                lead.full_name.lower()
            )
            score += name_similarity * 0.4
            total_weight += 0.4
        
        # Company similarity (weight: 0.3)
        if profile.company and lead.company:
            company_similarity = self._string_similarity(
                profile.company.lower(), 
                lead.company.lower()
            )
            score += company_similarity * 0.3
            total_weight += 0.3
        
        # Location similarity (weight: 0.2)
        if profile.location and lead.location:
            location_similarity = self._string_similarity(
                profile.location.lower(), 
                lead.location.lower()
            )
            score += location_similarity * 0.2
            total_weight += 0.2
        
        # Headline similarity (weight: 0.1)
        if profile.headline and lead.headline:
            headline_similarity = self._string_similarity(
                profile.headline.lower(), 
                lead.headline.lower()
            )
            score += headline_similarity * 0.1
            total_weight += 0.1
        
        return score / total_weight if total_weight > 0 else 0.0
    
    def _string_similarity(self, str1: str, str2: str) -> float:
        """
        Calculate string similarity using Levenshtein distance
        
        Args:
            str1: First string
            str2: Second string
            
        Returns:
            float: Similarity score (0.0 to 1.0)
        """
        if str1 == str2:
            return 1.0
        
        # Simple implementation - could use more sophisticated algorithms
        max_len = max(len(str1), len(str2))
        if max_len == 0:
            return 1.0
        
        # Calculate Levenshtein distance
        distance = self._levenshtein_distance(str1, str2)
        return 1.0 - (distance / max_len)
    
    def _levenshtein_distance(self, str1: str, str2: str) -> int:
        """Calculate Levenshtein distance between two strings"""
        if len(str1) < len(str2):
            return self._levenshtein_distance(str2, str1)
        
        if len(str2) == 0:
            return len(str1)
        
        previous_row = list(range(len(str2) + 1))
        for i, c1 in enumerate(str1):
            current_row = [i + 1]
            for j, c2 in enumerate(str2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]
    
    def _transform_profile_to_lead(
        self, 
        campaign_id: uuid.UUID, 
        profile: LinkedInProfile
    ) -> Dict[str, Any]:
        """
        Transform LinkedIn profile to lead database format
        
        Args:
            campaign_id: Campaign UUID
            profile: LinkedIn profile data
            
        Returns:
            Dict: Lead data for database insertion
        """
        # Extract first and last name
        first_name, last_name = self._parse_full_name(profile.full_name)
        
        # Calculate data quality score
        data_quality_score = self._calculate_data_quality_score(profile)
        
        # Extract experience years
        experience_years = self._calculate_experience_years(profile.experience)
        
        # Parse location components
        location_parts = self._parse_location(profile.location)
        
        lead_data = {
            'campaign_id': campaign_id,
            'linkedin_url': profile.url,
            'full_name': profile.full_name,
            'first_name': first_name,
            'last_name': last_name,
            'headline': profile.headline,
            'summary': profile.summary,
            
            # Location
            'location': profile.location,
            'country': location_parts.get('country'),
            'city': location_parts.get('city'),
            'region': location_parts.get('region'),
            
            # Current position
            'current_position': profile.current_position,
            'company': profile.company,
            'company_url': profile.company_url,
            'company_size': profile.company_size,
            'company_industry': profile.industry,
            
            # Professional details
            'industry': profile.industry,
            'experience_years': experience_years,
            
            # Skills and education
            'skills': profile.skills,
            'languages': profile.languages,
            'education': profile.education,
            'experience': profile.experience,
            'certifications': profile.certifications,
            
            # Contact information
            'email': profile.contact_info.get('email'),
            'phone': profile.contact_info.get('phone'),
            'website': profile.contact_info.get('website'),
            'twitter_handle': profile.contact_info.get('twitter'),
            'other_social': {
                k: v for k, v in profile.contact_info.items() 
                if k not in ['email', 'phone', 'website', 'twitter']
            },
            
            # Profile metrics
            'connections_count': profile.connections_count,
            'followers_count': profile.followers_count,
            'profile_image_url': profile.profile_image_url,
            'profile_completeness_score': self._calculate_profile_completeness(profile),
            
            # Data quality
            'data_quality_score': data_quality_score,
            'source': 'linkedin_scraping',
            'status': 'new',
            'priority': 'medium',
            
            # Timestamps
            'scraped_at': profile.scraped_at,
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }
        
        return lead_data
    
    def _parse_full_name(self, full_name: str) -> Tuple[Optional[str], Optional[str]]:
        """Parse full name into first and last name components"""
        if not full_name:
            return None, None
        
        name_parts = full_name.strip().split()
        if len(name_parts) == 0:
            return None, None
        elif len(name_parts) == 1:
            return name_parts[0], None
        else:
            return name_parts[0], ' '.join(name_parts[1:])
    
    def _parse_location(self, location: str) -> Dict[str, Optional[str]]:
        """Parse location string into components"""
        if not location:
            return {'country': None, 'city': None, 'region': None}
        
        # Simple parsing - could be enhanced with geocoding
        parts = [part.strip() for part in location.split(',')]
        
        if len(parts) == 1:
            return {'country': None, 'city': parts[0], 'region': None}
        elif len(parts) == 2:
            return {'country': parts[1], 'city': parts[0], 'region': None}
        elif len(parts) >= 3:
            return {'country': parts[-1], 'city': parts[0], 'region': parts[1]}
        
        return {'country': None, 'city': None, 'region': None}
    
    def _calculate_experience_years(self, experience: List[Dict[str, Any]]) -> Optional[int]:
        """Calculate total years of experience from experience data"""
        if not experience:
            return None
        
        total_months = 0
        for exp in experience:
            try:
                # Simple calculation - could be enhanced with date parsing
                start_date = exp.get('startDate')
                end_date = exp.get('endDate', 'Present')
                
                if start_date:
                    # Estimate 24 months per position if dates are unclear
                    total_months += 24
            except:
                continue
        
        return total_months // 12 if total_months > 0 else None
    
    def _calculate_data_quality_score(self, profile: LinkedInProfile) -> float:
        """Calculate data quality score based on profile completeness"""
        score = 0.0
        
        # Check each quality factor
        if profile.full_name:
            score += self.data_quality_weights['has_name']
        
        if profile.headline:
            score += self.data_quality_weights['has_headline']
        
        if profile.company:
            score += self.data_quality_weights['has_company']
        
        if profile.location:
            score += self.data_quality_weights['has_location']
        
        if profile.experience and len(profile.experience) > 0:
            score += self.data_quality_weights['has_experience']
        
        if profile.skills and len(profile.skills) > 0:
            score += self.data_quality_weights['has_skills']
        
        if profile.contact_info and any(profile.contact_info.values()):
            score += self.data_quality_weights['has_contact_info']
        
        return round(score, 2)
    
    def _calculate_profile_completeness(self, profile: LinkedInProfile) -> float:
        """Calculate LinkedIn profile completeness score"""
        completeness_factors = [
            bool(profile.full_name),
            bool(profile.headline),
            bool(profile.summary),
            bool(profile.company),
            bool(profile.location),
            bool(profile.experience and len(profile.experience) > 0),
            bool(profile.education and len(profile.education) > 0),
            bool(profile.skills and len(profile.skills) > 0),
            bool(profile.profile_image_url),
            bool(profile.connections_count and profile.connections_count > 0)
        ]
        
        return sum(completeness_factors) / len(completeness_factors)
    
    def _create_new_lead(self, lead_data: Dict[str, Any], db: Session):
        """Create new lead in database"""
        try:
            lead = Lead(**lead_data)
            db.add(lead)
            db.flush()  # Get the ID without committing
            
            logger.debug(f"Created new lead: {lead.id}")
            
        except IntegrityError as e:
            logger.warning(f"Integrity error creating lead: {e}")
            db.rollback()
            raise
    
    def _update_existing_lead(
        self, 
        existing_lead: Lead, 
        lead_data: Dict[str, Any], 
        db: Session
    ):
        """Update existing lead with new data"""
        try:
            # Update only if new data is more complete
            for field, value in lead_data.items():
                if field in ['id', 'campaign_id', 'created_at']:
                    continue  # Skip these fields
                
                current_value = getattr(existing_lead, field, None)
                
                # Update if current value is empty and new value exists
                if not current_value and value:
                    setattr(existing_lead, field, value)
                # Update timestamp fields
                elif field in ['updated_at', 'scraped_at']:
                    setattr(existing_lead, field, value)
            
            logger.debug(f"Updated existing lead: {existing_lead.id}")
            
        except Exception as e:
            logger.warning(f"Error updating lead: {e}")
            raise

# Global instance
lead_processor = LeadDataProcessor()
