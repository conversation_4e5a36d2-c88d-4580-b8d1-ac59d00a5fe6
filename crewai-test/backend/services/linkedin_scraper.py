# LinkedIn Scraper Service
# High-level service for managing LinkedIn lead scraping workflows

import asyncio
import logging
from typing import List, Dict, Any, Optional, Callable
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import uuid

from services.apify_client import (
    ApifyClient, 
    LinkedInSearchInput, 
    ApifyRunResult, 
    LinkedInProfile,
    ApifyRunStatus
)
from models.database import Campaign, Lead, User
from core.database import get_db
from core.config import settings

logger = logging.getLogger(__name__)

class ScrapingJobManager:
    """
    Manages LinkedIn scraping jobs with monitoring and result processing
    """
    
    def __init__(self, apify_client: ApifyClient = None):
        """
        Initialize scraping job manager
        
        Args:
            apify_client: Apify client instance (optional)
        """
        self.apify_client = apify_client or ApifyClient()
        self.active_jobs: Dict[str, Dict[str, Any]] = {}
        self.max_concurrent_jobs = settings.MAX_CONCURRENT_SCRAPING_JOBS
        self.job_timeout = settings.SCRAPING_TIMEOUT_MINUTES * 60
        
    async def start_campaign_scraping(
        self, 
        campaign_id: uuid.UUID,
        user_id: uuid.UUID,
        progress_callback: Optional[Callable] = None
    ) -> str:
        """
        Start LinkedIn scraping for a campaign
        
        Args:
            campaign_id: Campaign UUID
            user_id: User UUID
            progress_callback: Optional callback for progress updates
            
        Returns:
            str: Apify run ID
            
        Raises:
            Exception: If scraping cannot be started
        """
        try:
            # Get campaign from database
            db = next(get_db())
            campaign = db.query(Campaign).filter(
                Campaign.id == campaign_id,
                Campaign.user_id == user_id
            ).first()
            
            if not campaign:
                raise Exception("Campaign not found")
            
            # Check if already running
            if str(campaign_id) in self.active_jobs:
                raise Exception("Scraping already running for this campaign")
            
            # Check concurrent job limit
            if len(self.active_jobs) >= self.max_concurrent_jobs:
                raise Exception("Maximum concurrent scraping jobs reached")
            
            # Build search input from campaign
            search_input = LinkedInSearchInput(
                keywords=campaign.keywords,
                locations=campaign.target_locations,
                industries=campaign.target_industries,
                company_sizes=campaign.target_company_sizes,
                seniority_levels=campaign.target_seniority_levels,
                max_results=campaign.max_leads,
                include_private_profiles=campaign.include_private_profiles
            )
            
            # Start Apify actor
            run_id = await self.apify_client.start_linkedin_scraping(search_input)
            
            # Update campaign in database
            campaign.apify_run_id = run_id
            campaign.apify_status = ApifyRunStatus.RUNNING.value
            campaign.status = "active"
            campaign.started_at = datetime.utcnow()
            db.commit()
            db.close()
            
            # Track active job
            self.active_jobs[str(campaign_id)] = {
                "run_id": run_id,
                "campaign_id": campaign_id,
                "user_id": user_id,
                "started_at": datetime.utcnow(),
                "progress_callback": progress_callback,
                "status": ApifyRunStatus.RUNNING.value
            }
            
            # Start monitoring task
            asyncio.create_task(self._monitor_scraping_job(str(campaign_id)))
            
            logger.info(f"Started scraping for campaign {campaign_id}, run_id: {run_id}")
            return run_id
            
        except Exception as e:
            logger.error(f"Error starting campaign scraping: {e}")
            raise

    async def get_job_status(self, campaign_id: uuid.UUID) -> Optional[Dict[str, Any]]:
        """
        Get current status of a scraping job
        
        Args:
            campaign_id: Campaign UUID
            
        Returns:
            Dict[str, Any]: Job status information
        """
        try:
            campaign_key = str(campaign_id)
            
            if campaign_key not in self.active_jobs:
                # Check database for historical data
                db = next(get_db())
                campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
                db.close()
                
                if not campaign or not campaign.apify_run_id:
                    return None
                
                # Get status from Apify
                run_result = await self.apify_client.get_run_status(campaign.apify_run_id)
                
                return {
                    "campaign_id": campaign_id,
                    "run_id": campaign.apify_run_id,
                    "status": run_result.status.value,
                    "started_at": run_result.started_at,
                    "finished_at": run_result.finished_at,
                    "stats": run_result.stats,
                    "leads_found": campaign.leads_found,
                    "leads_processed": campaign.leads_processed
                }
            
            # Get live status for active job
            job_info = self.active_jobs[campaign_key]
            run_result = await self.apify_client.get_run_status(job_info["run_id"])
            
            # Calculate progress
            progress = self._calculate_progress(run_result, job_info)
            
            return {
                "campaign_id": campaign_id,
                "run_id": job_info["run_id"],
                "status": run_result.status.value,
                "started_at": run_result.started_at,
                "finished_at": run_result.finished_at,
                "stats": run_result.stats,
                "progress": progress,
                "estimated_completion": self._estimate_completion(run_result, job_info)
            }
            
        except Exception as e:
            logger.error(f"Error getting job status: {e}")
            return None

    async def stop_job(self, campaign_id: uuid.UUID) -> bool:
        """
        Stop a running scraping job
        
        Args:
            campaign_id: Campaign UUID
            
        Returns:
            bool: True if successfully stopped
        """
        try:
            campaign_key = str(campaign_id)
            
            if campaign_key not in self.active_jobs:
                return False
            
            job_info = self.active_jobs[campaign_key]
            
            # Abort Apify run
            success = await self.apify_client.abort_run(job_info["run_id"])
            
            if success:
                # Update database
                db = next(get_db())
                campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
                if campaign:
                    campaign.apify_status = ApifyRunStatus.ABORTED.value
                    campaign.status = "paused"
                    db.commit()
                db.close()
                
                # Remove from active jobs
                del self.active_jobs[campaign_key]
                
                logger.info(f"Stopped scraping job for campaign {campaign_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error stopping job: {e}")
            return False

    async def _monitor_scraping_job(self, campaign_key: str):
        """
        Monitor a scraping job until completion
        
        Args:
            campaign_key: Campaign ID as string
        """
        try:
            job_info = self.active_jobs.get(campaign_key)
            if not job_info:
                return
            
            run_id = job_info["run_id"]
            campaign_id = job_info["campaign_id"]
            user_id = job_info["user_id"]
            progress_callback = job_info.get("progress_callback")
            
            logger.info(f"Starting monitoring for scraping job {run_id}")
            
            # Monitoring loop
            poll_interval = 30  # seconds
            max_iterations = self.job_timeout // poll_interval
            
            for iteration in range(max_iterations):
                try:
                    # Get current status
                    run_result = await self.apify_client.get_run_status(run_id)
                    
                    # Update job status
                    job_info["status"] = run_result.status.value
                    
                    # Update database with current stats
                    await self._update_campaign_progress(campaign_id, run_result)
                    
                    # Call progress callback if provided
                    if progress_callback:
                        try:
                            await progress_callback(campaign_id, run_result)
                        except Exception as e:
                            logger.warning(f"Progress callback error: {e}")
                    
                    logger.info(f"Scraping job {run_id} status: {run_result.status.value}")
                    
                    # Check if completed
                    if run_result.status == ApifyRunStatus.SUCCEEDED:
                        await self._process_completed_job(campaign_id, run_id, user_id)
                        break
                    elif run_result.status in [ApifyRunStatus.FAILED, ApifyRunStatus.ABORTED, ApifyRunStatus.TIMED_OUT]:
                        await self._handle_failed_job(campaign_id, run_result)
                        break
                    
                    # Wait before next check
                    await asyncio.sleep(poll_interval)
                    
                except Exception as e:
                    logger.error(f"Error in monitoring iteration: {e}")
                    await asyncio.sleep(poll_interval)
            
            # Remove from active jobs
            if campaign_key in self.active_jobs:
                del self.active_jobs[campaign_key]
                
        except Exception as e:
            logger.error(f"Fatal error monitoring scraping job: {e}")
            # Cleanup
            if campaign_key in self.active_jobs:
                del self.active_jobs[campaign_key]

    async def _process_completed_job(self, campaign_id: uuid.UUID, run_id: str, user_id: uuid.UUID):
        """
        Process results from a completed scraping job
        
        Args:
            campaign_id: Campaign UUID
            run_id: Apify run ID
            user_id: User UUID
        """
        try:
            logger.info(f"Processing completed scraping job {run_id}")
            
            # Get results from Apify
            profiles = await self.apify_client.get_run_results(run_id)
            
            # Save results to database
            leads_created = await self._save_leads_to_database(campaign_id, profiles)
            
            # Update campaign status
            db = next(get_db())
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            if campaign:
                campaign.leads_found = leads_created
                campaign.status = "completed"
                campaign.apify_status = ApifyRunStatus.SUCCEEDED.value
                campaign.completed_at = datetime.utcnow()
            
            # Update user credits
            user = db.query(User).filter(User.id == user_id).first()
            if user:
                credits_used = max(1, leads_created // 10)  # 1 credit per 10 leads, minimum 1
                user.credits_remaining = max(0, user.credits_remaining - credits_used)
                user.credits_used += credits_used
            
            db.commit()
            db.close()
            
            logger.info(f"Successfully processed {leads_created} leads for campaign {campaign_id}")
            
        except Exception as e:
            logger.error(f"Error processing completed job: {e}")

    async def _save_leads_to_database(self, campaign_id: uuid.UUID, profiles: List[LinkedInProfile]) -> int:
        """
        Save LinkedIn profiles to database as leads
        
        Args:
            campaign_id: Campaign UUID
            profiles: List of LinkedIn profiles
            
        Returns:
            int: Number of leads created
        """
        try:
            db = next(get_db())
            leads_created = 0
            
            for profile in profiles:
                try:
                    # Check for duplicates
                    existing_lead = db.query(Lead).filter(
                        Lead.linkedin_url == profile.url
                    ).first()
                    
                    if existing_lead:
                        continue  # Skip duplicates
                    
                    # Create new lead
                    lead = Lead(
                        campaign_id=campaign_id,
                        linkedin_url=profile.url,
                        full_name=profile.full_name,
                        headline=profile.headline,
                        location=profile.location,
                        summary=profile.summary,
                        company=profile.company,
                        company_size=profile.company_size,
                        industry=profile.industry,
                        current_position=profile.current_position,
                        experience_years=self._calculate_experience_years(profile.experience),
                        education=profile.education,
                        skills=profile.skills,
                        contact_info=profile.contact_info,
                        profile_image_url=profile.profile_image_url,
                        connections_count=profile.connections_count,
                        scraped_at=profile.scraped_at,
                        status="new"
                    )
                    
                    db.add(lead)
                    leads_created += 1
                    
                except Exception as e:
                    logger.warning(f"Error saving lead: {e}")
                    continue
            
            db.commit()
            db.close()
            
            return leads_created
            
        except Exception as e:
            logger.error(f"Error saving leads to database: {e}")
            if 'db' in locals():
                db.rollback()
                db.close()
            return 0

    def _calculate_experience_years(self, experience: List[Dict[str, Any]]) -> Optional[int]:
        """Calculate total years of experience from experience data"""
        if not experience:
            return None
        
        total_months = 0
        for exp in experience:
            try:
                # Simple calculation - could be made more sophisticated
                start_date = exp.get("startDate")
                end_date = exp.get("endDate", "Present")
                
                if start_date:
                    # Estimate 24 months per position if dates are unclear
                    total_months += 24
            except:
                continue
        
        return total_months // 12 if total_months > 0 else None

    def _calculate_progress(self, run_result: ApifyRunResult, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate job progress information"""
        stats = run_result.stats
        items_found = stats.get("itemCount", 0)
        
        # Get target from job info or estimate
        target_items = 100  # Default estimate
        
        progress_percentage = min(100, (items_found / target_items) * 100) if target_items > 0 else 0
        
        return {
            "items_found": items_found,
            "target_items": target_items,
            "percentage": round(progress_percentage, 2),
            "runtime_seconds": stats.get("runTimeSecs", 0)
        }

    def _estimate_completion(self, run_result: ApifyRunResult, job_info: Dict[str, Any]) -> Optional[datetime]:
        """Estimate job completion time"""
        if run_result.status != ApifyRunStatus.RUNNING:
            return None
        
        stats = run_result.stats
        runtime_seconds = stats.get("runTimeSecs", 0)
        items_found = stats.get("itemCount", 0)
        
        if runtime_seconds > 0 and items_found > 0:
            rate = items_found / runtime_seconds  # items per second
            remaining_items = 100 - items_found  # estimate
            
            if rate > 0 and remaining_items > 0:
                estimated_seconds = remaining_items / rate
                return datetime.utcnow() + timedelta(seconds=estimated_seconds)
        
        return None

    async def _update_campaign_progress(self, campaign_id: uuid.UUID, run_result: ApifyRunResult):
        """Update campaign progress in database"""
        try:
            db = next(get_db())
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            
            if campaign:
                stats = run_result.stats
                campaign.leads_found = stats.get("itemCount", 0)
                campaign.apify_status = run_result.status.value
                campaign.updated_at = datetime.utcnow()
                db.commit()
            
            db.close()
            
        except Exception as e:
            logger.error(f"Error updating campaign progress: {e}")

    async def _handle_failed_job(self, campaign_id: uuid.UUID, run_result: ApifyRunResult):
        """Handle failed scraping job"""
        try:
            db = next(get_db())
            campaign = db.query(Campaign).filter(Campaign.id == campaign_id).first()
            
            if campaign:
                campaign.status = "failed"
                campaign.apify_status = run_result.status.value
                campaign.completed_at = datetime.utcnow()
                db.commit()
            
            db.close()
            
            logger.error(f"Scraping job failed for campaign {campaign_id}: {run_result.error_message}")
            
        except Exception as e:
            logger.error(f"Error handling failed job: {e}")

# Global scraping job manager
scraping_manager = ScrapingJobManager()
