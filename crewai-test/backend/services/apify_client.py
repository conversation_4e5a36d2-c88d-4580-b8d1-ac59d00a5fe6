# Apify API Client for LinkedIn Lead Scraping
# Secure integration with Apify's REST API for LinkedIn data extraction

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from pydantic import BaseModel, Field, validator
from enum import Enum
import os

from core.config import settings

logger = logging.getLogger(__name__)

class ApifyRunStatus(str, Enum):
    """Apify run status enumeration"""
    READY = "READY"
    RUNNING = "RUNNING" 
    SUCCEEDED = "SUCCEEDED"
    FAILED = "FAILED"
    TIMED_OUT = "TIMED-OUT"
    ABORTED = "ABORTED"

class LinkedInSearchInput(BaseModel):
    """Input configuration for LinkedIn scraper actor"""
    
    # Search parameters
    keywords: List[str] = Field(..., min_items=1, description="Search keywords")
    locations: Optional[List[str]] = Field(default=[], description="Target locations")
    industries: Optional[List[str]] = Field(default=[], description="Target industries") 
    company_sizes: Optional[List[str]] = Field(default=[], description="Company size filters")
    seniority_levels: Optional[List[str]] = Field(default=[], description="Seniority level filters")
    
    # Scraping limits
    max_results: int = Field(default=100, ge=1, le=2500, description="Maximum results to scrape")
    max_delay: int = Field(default=5, ge=1, le=30, description="Max delay between requests (seconds)")
    
    # Profile options
    include_private_profiles: bool = Field(default=False, description="Include private profiles")
    include_skills: bool = Field(default=True, description="Extract skills data")
    include_experience: bool = Field(default=True, description="Extract work experience")
    include_education: bool = Field(default=True, description="Extract education data")
    include_contact_info: bool = Field(default=True, description="Extract contact information")
    
    # Proxy settings
    use_residential_proxies: bool = Field(default=True, description="Use residential proxies")
    
    @validator('keywords')
    def validate_keywords(cls, v):
        """Validate and clean keywords"""
        if not v:
            raise ValueError("At least one keyword is required")
        # Remove empty strings and strip whitespace
        cleaned = [keyword.strip() for keyword in v if keyword.strip()]
        if not cleaned:
            raise ValueError("At least one non-empty keyword is required")
        return cleaned

class ApifyRunResult(BaseModel):
    """Apify run result data"""
    run_id: str
    status: ApifyRunStatus
    started_at: Optional[datetime]
    finished_at: Optional[datetime]
    stats: Dict[str, Any] = {}
    usage: Dict[str, Any] = {}
    error_message: Optional[str] = None

class LinkedInProfile(BaseModel):
    """LinkedIn profile data structure"""
    url: str
    full_name: Optional[str]
    headline: Optional[str]
    location: Optional[str]
    summary: Optional[str]
    company: Optional[str]
    company_url: Optional[str]
    company_size: Optional[str]
    industry: Optional[str]
    current_position: Optional[str]
    experience: List[Dict[str, Any]] = []
    education: List[Dict[str, Any]] = []
    skills: List[str] = []
    languages: List[str] = []
    connections_count: Optional[int]
    followers_count: Optional[int]
    profile_image_url: Optional[str]
    contact_info: Dict[str, Any] = {}
    scraped_at: datetime = Field(default_factory=datetime.utcnow)

class ApifyClient:
    """
    Secure Apify API client for LinkedIn lead scraping
    
    Handles authentication, rate limiting, and error handling
    """
    
    def __init__(self, api_token: str = None, actor_id: str = None):
        """
        Initialize Apify client
        
        Args:
            api_token: Apify API token (defaults to environment variable)
            actor_id: LinkedIn scraper actor ID (defaults to environment variable)
        """
        self.api_token = api_token or settings.APIFY_API_TOKEN
        self.actor_id = actor_id or settings.APIFY_ACTOR_ID
        self.base_url = "https://api.apify.com/v2"
        
        if not self.api_token:
            raise ValueError("Apify API token is required")
        if not self.actor_id:
            raise ValueError("Apify actor ID is required")
        
        # Request headers
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json",
            "User-Agent": "LinkedIn-Lead-Gen-API/1.0"
        }
        
        # Rate limiting
        self.max_concurrent_requests = 5
        self.request_delay = 1.0  # seconds between requests
        
        logger.info(f"Apify client initialized with actor: {self.actor_id}")

    async def start_linkedin_scraping(
        self, 
        search_input: LinkedInSearchInput,
        timeout: int = 300,
        memory_mbytes: int = 1024
    ) -> str:
        """
        Start LinkedIn scraping actor with given search parameters
        
        Args:
            search_input: Search configuration
            timeout: Actor timeout in seconds
            memory_mbytes: Memory allocation for actor
            
        Returns:
            str: Apify run ID
            
        Raises:
            Exception: If actor start fails
        """
        try:
            # Prepare actor input
            actor_input = self._build_actor_input(search_input)
            
            # Actor run configuration
            run_config = {
                "timeout": timeout,
                "memory": memory_mbytes,
                "build": "latest"
            }
            
            url = f"{self.base_url}/acts/{self.actor_id}/runs"
            
            async with aiohttp.ClientSession() as session:
                payload = {
                    **actor_input,
                    **run_config
                }
                
                logger.info(f"Starting LinkedIn scraping with {len(search_input.keywords)} keywords")
                logger.debug(f"Actor input: {json.dumps(payload, indent=2)}")
                
                async with session.post(
                    url, 
                    json=payload, 
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status == 201:
                        result = await response.json()
                        run_id = result["data"]["id"]
                        
                        logger.info(f"LinkedIn scraping started successfully. Run ID: {run_id}")
                        return run_id
                    
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to start scraping. Status: {response.status}, Error: {error_text}")
                        raise Exception(f"Apify actor start failed: {error_text}")
                        
        except aiohttp.ClientError as e:
            logger.error(f"Network error starting scraping: {e}")
            raise Exception(f"Network error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error starting scraping: {e}")
            raise

    async def get_run_status(self, run_id: str) -> ApifyRunResult:
        """
        Get current status of an Apify run
        
        Args:
            run_id: Apify run ID
            
        Returns:
            ApifyRunResult: Run status and metadata
            
        Raises:
            Exception: If status check fails
        """
        try:
            url = f"{self.base_url}/actor-runs/{run_id}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, 
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=15)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        run_data = result["data"]
                        
                        # Parse timestamps
                        started_at = None
                        finished_at = None
                        
                        if run_data.get("startedAt"):
                            started_at = datetime.fromisoformat(
                                run_data["startedAt"].replace("Z", "+00:00")
                            )
                        
                        if run_data.get("finishedAt"):
                            finished_at = datetime.fromisoformat(
                                run_data["finishedAt"].replace("Z", "+00:00")
                            )
                        
                        return ApifyRunResult(
                            run_id=run_id,
                            status=ApifyRunStatus(run_data["status"]),
                            started_at=started_at,
                            finished_at=finished_at,
                            stats=run_data.get("stats", {}),
                            usage=run_data.get("usage", {}),
                            error_message=run_data.get("statusMessage")
                        )
                    
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get run status. Status: {response.status}, Error: {error_text}")
                        raise Exception(f"Failed to get run status: {error_text}")
                        
        except aiohttp.ClientError as e:
            logger.error(f"Network error getting run status: {e}")
            raise Exception(f"Network error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error getting run status: {e}")
            raise

    async def get_run_results(
        self, 
        run_id: str, 
        format: str = "json",
        limit: Optional[int] = None,
        offset: int = 0
    ) -> List[LinkedInProfile]:
        """
        Fetch results from completed Apify run
        
        Args:
            run_id: Apify run ID
            format: Result format (json, csv, etc.)
            limit: Maximum number of results to fetch
            offset: Number of results to skip
            
        Returns:
            List[LinkedInProfile]: List of scraped LinkedIn profiles
            
        Raises:
            Exception: If results fetch fails
        """
        try:
            # Build URL with query parameters
            url = f"{self.base_url}/actor-runs/{run_id}/dataset/items"
            params = {
                "format": format,
                "offset": offset
            }
            
            if limit:
                params["limit"] = limit
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url, 
                    headers=self.headers,
                    params=params,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        raw_results = await response.json()
                        
                        logger.info(f"Retrieved {len(raw_results)} results from run {run_id}")
                        
                        # Transform raw results to LinkedInProfile objects
                        profiles = []
                        for item in raw_results:
                            try:
                                profile = self._transform_profile_data(item)
                                profiles.append(profile)
                            except Exception as e:
                                logger.warning(f"Error transforming profile data: {e}")
                                continue
                        
                        logger.info(f"Successfully transformed {len(profiles)} profiles")
                        return profiles
                    
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to get results. Status: {response.status}, Error: {error_text}")
                        raise Exception(f"Failed to get results: {error_text}")
                        
        except aiohttp.ClientError as e:
            logger.error(f"Network error getting results: {e}")
            raise Exception(f"Network error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error getting results: {e}")
            raise

    async def abort_run(self, run_id: str) -> bool:
        """
        Abort a running Apify actor
        
        Args:
            run_id: Apify run ID
            
        Returns:
            bool: True if successfully aborted
            
        Raises:
            Exception: If abort fails
        """
        try:
            url = f"{self.base_url}/actor-runs/{run_id}/abort"
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url, 
                    headers=self.headers,
                    timeout=aiohttp.ClientTimeout(total=15)
                ) as response:
                    
                    if response.status == 200:
                        logger.info(f"Successfully aborted run {run_id}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to abort run. Status: {response.status}, Error: {error_text}")
                        raise Exception(f"Failed to abort run: {error_text}")
                        
        except aiohttp.ClientError as e:
            logger.error(f"Network error aborting run: {e}")
            raise Exception(f"Network error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error aborting run: {e}")
            raise

    def _build_actor_input(self, search_input: LinkedInSearchInput) -> Dict[str, Any]:
        """
        Build actor input configuration from search parameters
        
        Args:
            search_input: Search configuration
            
        Returns:
            Dict[str, Any]: Actor input configuration
        """
        # Build search query from keywords
        search_query = " OR ".join(f'"{keyword}"' for keyword in search_input.keywords)
        
        actor_input = {
            # Search configuration
            "searchQuery": search_query,
            "maxResults": search_input.max_results,
            "maxDelay": search_input.max_delay,
            
            # Profile extraction options
            "includePrivateProfiles": search_input.include_private_profiles,
            "extractSkills": search_input.include_skills,
            "extractExperience": search_input.include_experience,
            "extractEducation": search_input.include_education,
            "extractContactInfo": search_input.include_contact_info,
            
            # Proxy configuration
            "proxyConfiguration": {
                "useApifyProxy": True,
                "apifyProxyGroups": ["RESIDENTIAL"] if search_input.use_residential_proxies else ["DATACENTER"]
            },
            
            # Output configuration
            "saveToDataset": True,
            "outputFormat": "json"
        }
        
        # Add location filter if specified
        if search_input.locations:
            actor_input["locationFilter"] = search_input.locations
        
        # Add industry filter if specified
        if search_input.industries:
            actor_input["industryFilter"] = search_input.industries
        
        # Add company size filter if specified
        if search_input.company_sizes:
            actor_input["companySizeFilter"] = search_input.company_sizes
        
        # Add seniority filter if specified
        if search_input.seniority_levels:
            actor_input["seniorityFilter"] = search_input.seniority_levels
        
        return actor_input

    def _transform_profile_data(self, raw_data: Dict[str, Any]) -> LinkedInProfile:
        """
        Transform raw Apify result to LinkedInProfile
        
        Args:
            raw_data: Raw profile data from Apify
            
        Returns:
            LinkedInProfile: Structured profile data
        """
        # Extract contact information
        contact_info = {}
        if raw_data.get("email"):
            contact_info["email"] = raw_data["email"]
        if raw_data.get("phone"):
            contact_info["phone"] = raw_data["phone"]
        if raw_data.get("website"):
            contact_info["website"] = raw_data["website"]
        if raw_data.get("twitter"):
            contact_info["twitter"] = raw_data["twitter"]
        
        # Extract experience years from experience data
        experience_data = raw_data.get("experience", [])
        
        return LinkedInProfile(
            url=raw_data.get("url", ""),
            full_name=raw_data.get("fullName"),
            headline=raw_data.get("headline"),
            location=raw_data.get("location"),
            summary=raw_data.get("summary"),
            company=raw_data.get("company"),
            company_url=raw_data.get("companyUrl"),
            company_size=raw_data.get("companySize"),
            industry=raw_data.get("industry"),
            current_position=raw_data.get("currentPosition"),
            experience=experience_data,
            education=raw_data.get("education", []),
            skills=raw_data.get("skills", []),
            languages=raw_data.get("languages", []),
            connections_count=raw_data.get("connectionsCount"),
            followers_count=raw_data.get("followersCount"),
            profile_image_url=raw_data.get("profileImageUrl"),
            contact_info=contact_info
        )

# Global Apify client instance
apify_client = ApifyClient()
