from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from crewai import Agent, Task, Crew
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv
import os
from typing import Optional
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="CrewAI Agent API",
    description="A FastAPI backend for CrewAI agents",
    version="1.0.0"
)

# Pydantic models for request/response
class TaskRequest(BaseModel):
    description: str
    expected_output: str
    agent_role: Optional[str] = "Assistant"
    agent_goal: Optional[str] = "Help the user with their request"
    agent_backstory: Optional[str] = "A helpful AI assistant"

class TaskResponse(BaseModel):
    task_id: str
    result: str
    status: str

class AgentRequest(BaseModel):
    role: str
    goal: str
    backstory: str
    task_description: str
    expected_output: str

# Initialize the default test agent
def create_default_agent():
    return Agent(
        role="Assistant",
        goal="Help users with their requests",
        backstory="A helpful AI assistant ready to tackle any task.",
        verbose=True,
        llm=ChatOpenAI(model="gpt-3.5-turbo", temperature=0.2)
    )

# Thread pool for running CrewAI tasks
executor = ThreadPoolExecutor(max_workers=4)

def run_crew_task(agent, task):
    """Run a CrewAI task in a separate thread"""
    crew = Crew(
        agents=[agent],
        tasks=[task],
        verbose=True
    )
    result = crew.kickoff()
    return str(result)

@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "CrewAI FastAPI Backend is running!", "status": "healthy"}

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "service": "CrewAI FastAPI Backend",
        "version": "1.0.0",
        "openai_configured": bool(os.getenv("OPENAI_API_KEY"))
    }

@app.post("/run-task", response_model=TaskResponse)
async def run_task(request: TaskRequest):
    """Run a task with the default agent"""
    try:
        # Create agent
        agent = Agent(
            role=request.agent_role,
            goal=request.agent_goal,
            backstory=request.agent_backstory,
            verbose=True,
            llm=ChatOpenAI(model="gpt-3.5-turbo", temperature=0.2)
        )
        
        # Create task
        task = Task(
            description=request.description,
            expected_output=request.expected_output,
            agent=agent
        )
        
        # Run the task in a separate thread to avoid blocking
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(executor, run_crew_task, agent, task)
        
        return TaskResponse(
            task_id="task_001",  # You could generate unique IDs here
            result=result,
            status="completed"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running task: {str(e)}")

@app.post("/run-custom-agent", response_model=TaskResponse)
async def run_custom_agent(request: AgentRequest):
    """Run a task with a custom agent"""
    try:
        # Create custom agent
        agent = Agent(
            role=request.role,
            goal=request.goal,
            backstory=request.backstory,
            verbose=True,
            llm=ChatOpenAI(model="gpt-3.5-turbo", temperature=0.2)
        )
        
        # Create task
        task = Task(
            description=request.task_description,
            expected_output=request.expected_output,
            agent=agent
        )
        
        # Run the task in a separate thread
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(executor, run_crew_task, agent, task)
        
        return TaskResponse(
            task_id="custom_task_001",
            result=result,
            status="completed"
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error running custom agent: {str(e)}")

@app.post("/test-agent")
async def test_agent():
    """Test the basic agent functionality"""
    try:
        # Create test agent
        agent = create_default_agent()
        
        # Create simple test task
        task = Task(
            description="Say hello and confirm the API is working",
            expected_output="A friendly greeting confirming the API is operational",
            agent=agent
        )
        
        # Run the task
        loop = asyncio.get_event_loop()
        result = await loop.run_in_executor(executor, run_crew_task, agent, task)
        
        return {
            "message": "Test completed successfully",
            "result": result,
            "status": "success"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Test failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
