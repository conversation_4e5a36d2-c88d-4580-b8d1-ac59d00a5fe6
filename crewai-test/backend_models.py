# Backend Models and Schemas for LinkedIn Lead Generation App

from sqlalchemy import Column, String, Integer, DateTime, Text, ARRAY, JSON, DECIMAL, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

Base = declarative_base()

# SQLAlchemy Models
class User(Base):
    __tablename__ = "users"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    first_name = Column(String(100))
    last_name = Column(String(100))
    company = Column(String(255))
    subscription_tier = Column(String(50), default="free")
    credits_remaining = Column(Integer, default=100)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    campaigns = relationship("Campaign", back_populates="user")
    exports = relationship("Export", back_populates="user")

class Campaign(Base):
    __tablename__ = "campaigns"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    keywords = Column(ARRAY(String), nullable=False)
    target_locations = Column(ARRAY(String))
    target_industries = Column(ARRAY(String))
    target_company_sizes = Column(ARRAY(String))
    target_seniority_levels = Column(ARRAY(String))
    max_leads = Column(Integer, default=1000)
    status = Column(String(50), default="active")
    apify_run_id = Column(String(255))
    leads_found = Column(Integer, default=0)
    leads_processed = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    user = relationship("User", back_populates="campaigns")
    leads = relationship("Lead", back_populates="campaign")

class Lead(Base):
    __tablename__ = "leads"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    campaign_id = Column(UUID(as_uuid=True), ForeignKey("campaigns.id"), nullable=False)
    linkedin_url = Column(String(500), unique=True)
    full_name = Column(String(255))
    headline = Column(String(500))
    location = Column(String(255))
    company = Column(String(255))
    company_size = Column(String(100))
    industry = Column(String(255))
    experience_years = Column(Integer)
    education = Column(JSON)
    skills = Column(ARRAY(String))
    ai_score = Column(DECIMAL(3,2))  # 0.00 to 1.00
    ai_reasoning = Column(Text)
    contact_info = Column(JSON)
    profile_image_url = Column(String(500))
    connections_count = Column(Integer)
    scraped_at = Column(DateTime, default=datetime.utcnow)
    processed_at = Column(DateTime)
    status = Column(String(50), default="new")  # new, qualified, contacted, converted, rejected
    notes = Column(Text)
    
    campaign = relationship("Campaign", back_populates="leads")
    scoring_history = relationship("ScoringHistory", back_populates="lead")

class ScoringHistory(Base):
    __tablename__ = "scoring_history"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    lead_id = Column(UUID(as_uuid=True), ForeignKey("leads.id"), nullable=False)
    score = Column(DECIMAL(3,2))
    reasoning = Column(Text)
    model_version = Column(String(50))
    criteria_weights = Column(JSON)
    scored_at = Column(DateTime, default=datetime.utcnow)
    
    lead = relationship("Lead", back_populates="scoring_history")

class Export(Base):
    __tablename__ = "exports"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    campaign_id = Column(UUID(as_uuid=True), ForeignKey("campaigns.id"))
    format = Column(String(20))  # csv, xlsx, json
    filters = Column(JSON)
    file_path = Column(String(500))
    status = Column(String(50), default="pending")  # pending, processing, completed, failed
    total_records = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    
    user = relationship("User", back_populates="exports")

# Pydantic Schemas
class UserBase(BaseModel):
    email: EmailStr
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    company: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserResponse(UserBase):
    id: uuid.UUID
    subscription_tier: str
    credits_remaining: int
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class CampaignBase(BaseModel):
    name: str
    keywords: List[str]
    target_locations: Optional[List[str]] = []
    target_industries: Optional[List[str]] = []
    target_company_sizes: Optional[List[str]] = []
    target_seniority_levels: Optional[List[str]] = []
    max_leads: Optional[int] = 1000

class CampaignCreate(CampaignBase):
    pass

class CampaignResponse(CampaignBase):
    id: uuid.UUID
    user_id: uuid.UUID
    status: str
    leads_found: int
    leads_processed: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class LeadBase(BaseModel):
    linkedin_url: Optional[str] = None
    full_name: Optional[str] = None
    headline: Optional[str] = None
    location: Optional[str] = None
    company: Optional[str] = None
    company_size: Optional[str] = None
    industry: Optional[str] = None
    experience_years: Optional[int] = None
    education: Optional[Dict[str, Any]] = None
    skills: Optional[List[str]] = []
    contact_info: Optional[Dict[str, Any]] = None

class LeadResponse(LeadBase):
    id: uuid.UUID
    campaign_id: uuid.UUID
    ai_score: Optional[float] = None
    ai_reasoning: Optional[str] = None
    status: str
    scraped_at: datetime
    processed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class ScoringRequest(BaseModel):
    lead_id: uuid.UUID
    criteria_weights: Optional[Dict[str, float]] = None

class ScoringResponse(BaseModel):
    lead_id: uuid.UUID
    score: float
    reasoning: str
    criteria_breakdown: Dict[str, float]
    
class ExportRequest(BaseModel):
    campaign_id: Optional[uuid.UUID] = None
    format: str = Field(..., regex="^(csv|xlsx|json)$")
    filters: Optional[Dict[str, Any]] = None
    min_score: Optional[float] = None
    status_filter: Optional[List[str]] = None

class ExportResponse(BaseModel):
    id: uuid.UUID
    status: str
    total_records: Optional[int] = None
    file_path: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True
