import requests
import json

# Base URL for your FastAPI server
BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_basic_endpoint():
    """Test the root endpoint"""
    print("🔍 Testing root endpoint...")
    response = requests.get(f"{BASE_URL}/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_agent():
    """Test the agent functionality"""
    print("🤖 Testing agent...")
    response = requests.post(f"{BASE_URL}/test-agent")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_run_task():
    """Test running a custom task"""
    print("📋 Testing custom task...")
    
    task_data = {
        "description": "Write a short poem about artificial intelligence",
        "expected_output": "A creative poem about AI with 4-6 lines",
        "agent_role": "Poet",
        "agent_goal": "Create beautiful and meaningful poetry",
        "agent_backstory": "A creative AI poet who loves to express ideas through verse"
    }
    
    response = requests.post(f"{BASE_URL}/run-task", json=task_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def test_custom_agent():
    """Test running a custom agent"""
    print("🎭 Testing custom agent...")
    
    agent_data = {
        "role": "Data Analyst",
        "goal": "Analyze data and provide insights",
        "backstory": "An experienced data analyst with expertise in finding patterns and trends",
        "task_description": "Explain the importance of data visualization in business decision making",
        "expected_output": "A clear explanation of why data visualization is crucial for business decisions"
    }
    
    response = requests.post(f"{BASE_URL}/run-custom-agent", json=agent_data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print("-" * 50)

def main():
    """Run all tests"""
    print("🚀 Starting API tests...\n")
    
    try:
        test_health_check()
        test_basic_endpoint()
        test_agent()
        test_run_task()
        test_custom_agent()
        
        print("✅ All tests completed!")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API server.")
        print("Make sure the FastAPI server is running on http://localhost:8000")
        print("Run: python app.py")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
